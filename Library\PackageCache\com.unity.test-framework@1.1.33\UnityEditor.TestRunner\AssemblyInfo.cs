using System.Reflection;
using System.Runtime.CompilerServices;

[assembly: <PERSON><PERSON><PERSON><PERSON>("UnityEditor.TestRunner")]
[assembly: InternalsVisibleTo("Assembly-CSharp-Editor-testable")]
[assembly: InternalsVisibleTo("DynamicProxyGenAssembly2")]
[assembly: InternalsVisibleTo("Unity.PerformanceTesting.Editor")]
[assembly: InternalsVisibleTo("Unity.IntegrationTests")]
[assembly: InternalsVisibleTo("UnityEditor.TestRunner.Tests")]
[assembly: InternalsVisibleTo("Unity.PackageManagerUI.Develop.Editor")]
[assembly: InternalsVisibleTo("Unity.PackageManagerUI.Develop.EditorTests")]
[assembly: InternalsVisibleTo("Unity.PackageValidationSuite.Editor")]

[assembly: AssemblyVersion("1.0.0")]
