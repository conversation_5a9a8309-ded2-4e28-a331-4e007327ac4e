L.  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL      _DISTORTION    _LINEAR_TO_SRGB_CONVERSION     _TONEMAP_NEUTRALF+  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	vec4 _Distortion_Params1;
uniform 	vec4 _Distortion_Params2;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(2) uniform mediump sampler2D _UserLut;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec3 u_xlat0;
mediump vec3 u_xlat10_0;
uint u_xlatu0;
bvec3 u_xlatb0;
vec3 u_xlat1;
mediump vec3 u_xlat16_1;
vec3 u_xlat2;
mediump vec3 u_xlat10_2;
vec2 u_xlat3;
mediump vec3 u_xlat16_4;
mediump vec3 u_xlat16_5;
mediump vec3 u_xlat16_6;
vec2 u_xlat7;
mediump vec3 u_xlat10_7;
bool u_xlatb10;
vec2 u_xlat15;
bool u_xlatb15;
float u_xlat21;
bool u_xlatb21;
float u_xlat22;
float u_xlat23;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat7.xy = vs_TEXCOORD0.xy + vec2(-0.5, -0.5);
    u_xlat1.xy = u_xlat7.xy * _Distortion_Params2.zz + vec2(0.5, 0.5);
    u_xlat7.xy = u_xlat7.xy * _Distortion_Params2.zz + (-_Distortion_Params1.xy);
    u_xlat7.xy = u_xlat7.xy * _Distortion_Params1.zw;
    u_xlat21 = dot(u_xlat7.xy, u_xlat7.xy);
    u_xlat21 = sqrt(u_xlat21);
#ifdef UNITY_ADRENO_ES3
    u_xlatb15 = !!(0.0<_Distortion_Params2.w);
#else
    u_xlatb15 = 0.0<_Distortion_Params2.w;
#endif
    if(u_xlatb15){
        u_xlat15.xy = vec2(u_xlat21) * _Distortion_Params2.xy;
        u_xlat2.x = sin(u_xlat15.x);
        u_xlat3.x = cos(u_xlat15.x);
        u_xlat15.x = u_xlat2.x / u_xlat3.x;
        u_xlat22 = float(1.0) / float(u_xlat15.y);
        u_xlat15.x = u_xlat15.x * u_xlat22 + -1.0;
        u_xlat2.xy = u_xlat7.xy * u_xlat15.xx + u_xlat1.xy;
    } else {
        u_xlat15.x = float(1.0) / float(u_xlat21);
        u_xlat15.x = u_xlat15.x * _Distortion_Params2.x;
        u_xlat21 = u_xlat21 * _Distortion_Params2.y;
        u_xlat22 = min(abs(u_xlat21), 1.0);
        u_xlat23 = max(abs(u_xlat21), 1.0);
        u_xlat23 = float(1.0) / u_xlat23;
        u_xlat22 = u_xlat22 * u_xlat23;
        u_xlat23 = u_xlat22 * u_xlat22;
        u_xlat3.x = u_xlat23 * 0.0208350997 + -0.0851330012;
        u_xlat3.x = u_xlat23 * u_xlat3.x + 0.180141002;
        u_xlat3.x = u_xlat23 * u_xlat3.x + -0.330299497;
        u_xlat23 = u_xlat23 * u_xlat3.x + 0.999866009;
        u_xlat3.x = u_xlat22 * u_xlat23;
#ifdef UNITY_ADRENO_ES3
        u_xlatb10 = !!(1.0<abs(u_xlat21));
#else
        u_xlatb10 = 1.0<abs(u_xlat21);
#endif
        u_xlat3.x = u_xlat3.x * -2.0 + 1.57079637;
        u_xlat3.x = u_xlatb10 ? u_xlat3.x : float(0.0);
        u_xlat22 = u_xlat22 * u_xlat23 + u_xlat3.x;
        u_xlat21 = min(u_xlat21, 1.0);
#ifdef UNITY_ADRENO_ES3
        u_xlatb21 = !!(u_xlat21<(-u_xlat21));
#else
        u_xlatb21 = u_xlat21<(-u_xlat21);
#endif
        u_xlat21 = (u_xlatb21) ? (-u_xlat22) : u_xlat22;
        u_xlat21 = u_xlat15.x * u_xlat21 + -1.0;
        u_xlat2.xy = u_xlat7.xy * vec2(u_xlat21) + u_xlat1.xy;
    }
    u_xlat2.z = float(u_xlatu0);
    u_xlat10_0.xyz = texture(_SourceTex, u_xlat2.xyz).xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlatb21 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb21 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb21){
        u_xlat1.xy = u_xlat2.xy + (-_Vignette_Params2.xy);
        u_xlat1.yz = abs(u_xlat1.xy) * _Vignette_Params2.zz;
        u_xlat1.x = u_xlat1.y * _Vignette_Params1.w;
        u_xlat21 = dot(u_xlat1.xz, u_xlat1.xz);
        u_xlat21 = (-u_xlat21) + 1.0;
        u_xlat21 = max(u_xlat21, 0.0);
        u_xlat21 = log2(u_xlat21);
        u_xlat21 = u_xlat21 * _Vignette_Params2.w;
        u_xlat21 = exp2(u_xlat21);
        u_xlat1.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat1.xyz = vec3(u_xlat21) * u_xlat1.xyz + _Vignette_Params1.xyz;
        u_xlat1.xyz = u_xlat10_0.xyz * u_xlat1.xyz;
        u_xlat16_1.xyz = u_xlat1.xyz;
    } else {
        u_xlat16_1.xyz = u_xlat10_0.xyz;
    }
    u_xlat0.xyz = u_xlat16_1.xyz * _Lut_Params.www;
    u_xlat16_4.xyz = min(u_xlat0.xyz, vec3(435.187134, 435.187134, 435.187134));
    u_xlat16_5.xyz = u_xlat16_4.xyz * vec3(1.31338608, 1.31338608, 1.31338608);
    u_xlat16_6.xyz = u_xlat16_4.xyz * vec3(0.262677222, 0.262677222, 0.262677222) + vec3(0.0695999935, 0.0695999935, 0.0695999935);
    u_xlat16_6.xyz = u_xlat16_5.xyz * u_xlat16_6.xyz + vec3(0.00543999998, 0.00543999998, 0.00543999998);
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(0.262677222, 0.262677222, 0.262677222) + vec3(0.289999992, 0.289999992, 0.289999992);
    u_xlat16_4.xyz = u_xlat16_5.xyz * u_xlat16_4.xyz + vec3(0.0816000104, 0.0816000104, 0.0816000104);
    u_xlat16_4.xyz = u_xlat16_6.xyz / u_xlat16_4.xyz;
    u_xlat16_4.xyz = u_xlat16_4.xyz + vec3(-0.0666666627, -0.0666666627, -0.0666666627);
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(1.31338608, 1.31338608, 1.31338608);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_4.xyz = min(max(u_xlat16_4.xyz, 0.0), 1.0);
#else
    u_xlat16_4.xyz = clamp(u_xlat16_4.xyz, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb0.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb0.x){
        u_xlat16_5.xyz = u_xlat16_4.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_6.xyz = log2(u_xlat16_4.xyz);
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_6.xyz = exp2(u_xlat16_6.xyz);
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_4.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_5;
            hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_5.x : u_xlat16_6.x;
            hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_5.y : u_xlat16_6.y;
            hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_5.z : u_xlat16_6.z;
            u_xlat16_5 = hlslcc_movcTemp;
        }
        u_xlat0.xyz = u_xlat16_5.zxy * _UserLut_Params.zzz;
        u_xlat0.x = floor(u_xlat0.x);
        u_xlat2.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat2.yz = u_xlat0.yz * _UserLut_Params.xy + u_xlat2.xy;
        u_xlat2.x = u_xlat0.x * _UserLut_Params.y + u_xlat2.y;
        u_xlat10_7.xyz = textureLod(_UserLut, u_xlat2.xz, 0.0).xyz;
        u_xlat3.x = _UserLut_Params.y;
        u_xlat3.y = 0.0;
        u_xlat2.xy = u_xlat2.xz + u_xlat3.xy;
        u_xlat10_2.xyz = textureLod(_UserLut, u_xlat2.xy, 0.0).xyz;
        u_xlat0.x = u_xlat16_5.z * _UserLut_Params.z + (-u_xlat0.x);
        u_xlat2.xyz = (-u_xlat10_7.xyz) + u_xlat10_2.xyz;
        u_xlat0.xyz = u_xlat0.xxx * u_xlat2.xyz + u_xlat10_7.xyz;
        u_xlat0.xyz = (-u_xlat16_5.xyz) + u_xlat0.xyz;
        u_xlat0.xyz = _UserLut_Params.www * u_xlat0.xyz + u_xlat16_5.xyz;
        u_xlat16_5.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_6.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_6.xyz = log2(abs(u_xlat16_6.xyz));
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_6.xyz = exp2(u_xlat16_6.xyz);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
        u_xlat16_4.x = (u_xlatb0.x) ? u_xlat16_5.x : u_xlat16_6.x;
        u_xlat16_4.y = (u_xlatb0.y) ? u_xlat16_5.y : u_xlat16_6.y;
        u_xlat16_4.z = (u_xlatb0.z) ? u_xlat16_5.z : u_xlat16_6.z;
    }
    u_xlat0.xyz = u_xlat16_4.zxy * _Lut_Params.zzz;
    u_xlat0.x = floor(u_xlat0.x);
    u_xlat2.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat2.yz = u_xlat0.yz * _Lut_Params.xy + u_xlat2.xy;
    u_xlat2.x = u_xlat0.x * _Lut_Params.y + u_xlat2.y;
    u_xlat10_7.xyz = textureLod(_InternalLut, u_xlat2.xz, 0.0).xyz;
    u_xlat3.x = _Lut_Params.y;
    u_xlat3.y = 0.0;
    u_xlat2.xy = u_xlat2.xz + u_xlat3.xy;
    u_xlat10_2.xyz = textureLod(_InternalLut, u_xlat2.xy, 0.0).xyz;
    u_xlat0.x = u_xlat16_4.z * _Lut_Params.z + (-u_xlat0.x);
    u_xlat2.xyz = (-u_xlat10_7.xyz) + u_xlat10_2.xyz;
    u_xlat0.xyz = u_xlat0.xxx * u_xlat2.xyz + u_xlat10_7.xyz;
    u_xlat16_4.xyz = u_xlat0.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlat16_5.xyz = log2(abs(u_xlat0.xyz));
    u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_5.xyz = exp2(u_xlat16_5.xyz);
    u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat0.xyzx).xyz;
    SV_Target0.x = (u_xlatb0.x) ? u_xlat16_4.x : u_xlat16_5.x;
    SV_Target0.y = (u_xlatb0.y) ? u_xlat16_4.y : u_xlat16_5.y;
    SV_Target0.z = (u_xlatb0.z) ? u_xlat16_4.z : u_xlat16_5.z;
    SV_Target0.w = 1.0;
    return;
}

#endif
                                $Globals`         _Lut_Params                          _UserLut_Params                         _Distortion_Params1                          _Distortion_Params2                   0      _Vignette_Params1                     @      _Vignette_Params2                     P          $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      _InternalLut                _UserLut                UnityStereoEyeIndices                  