0+  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL      _CHROMATIC_ABERRATION   
   _DITHERING  (  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	float _Chroma_Params;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
uniform 	vec4 _Dithering_Params;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(2) uniform mediump sampler2D _UserLut;
UNITY_LOCATION(3) uniform mediump sampler2D _BlueNoise_Texture;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec3 u_xlat0;
mediump vec3 u_xlat16_0;
uint u_xlatu0;
bool u_xlatb0;
vec4 u_xlat1;
bvec3 u_xlatb1;
mediump vec3 u_xlat10_2;
mediump vec3 u_xlat16_3;
mediump vec3 u_xlat16_4;
mediump vec3 u_xlat16_5;
vec3 u_xlat6;
mediump vec3 u_xlat10_6;
bool u_xlatb6;
vec2 u_xlat7;
vec3 u_xlat8;
mediump vec3 u_xlat10_9;
float u_xlat25;
mediump float u_xlat10_25;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat1 = vs_TEXCOORD0.xyxy * vec4(2.0, 2.0, 2.0, 2.0) + vec4(-1.0, -1.0, -1.0, -1.0);
    u_xlat8.x = dot(u_xlat1.zw, u_xlat1.zw);
    u_xlat1 = u_xlat8.xxxx * u_xlat1;
    u_xlat1 = u_xlat1 * vec4(_Chroma_Params);
    u_xlat0.z = float(u_xlatu0);
    u_xlat0.xy = vs_TEXCOORD0.xy;
    u_xlat10_2.x = texture(_SourceTex, u_xlat0.xyz).x;
    u_xlat1 = u_xlat1 * vec4(-0.333333343, -0.333333343, -0.666666687, -0.666666687) + vs_TEXCOORD0.xyxy;
    u_xlat0.xy = u_xlat1.xy;
    u_xlat10_2.y = texture(_SourceTex, u_xlat0.xyz).y;
    u_xlat0.xy = u_xlat1.zw;
    u_xlat10_2.z = texture(_SourceTex, u_xlat0.xyz).z;
#ifdef UNITY_ADRENO_ES3
    u_xlatb0 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb0 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb0){
        u_xlat0.xy = vs_TEXCOORD0.xy + (-_Vignette_Params2.xy);
        u_xlat0.yz = abs(u_xlat0.xy) * _Vignette_Params2.zz;
        u_xlat0.x = u_xlat0.y * _Vignette_Params1.w;
        u_xlat0.x = dot(u_xlat0.xz, u_xlat0.xz);
        u_xlat0.x = (-u_xlat0.x) + 1.0;
        u_xlat0.x = max(u_xlat0.x, 0.0);
        u_xlat0.x = log2(u_xlat0.x);
        u_xlat0.x = u_xlat0.x * _Vignette_Params2.w;
        u_xlat0.x = exp2(u_xlat0.x);
        u_xlat8.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat0.xyz = u_xlat0.xxx * u_xlat8.xyz + _Vignette_Params1.xyz;
        u_xlat0.xyz = u_xlat0.xyz * u_xlat10_2.xyz;
        u_xlat16_0.xyz = u_xlat0.xyz;
    } else {
        u_xlat16_0.xyz = u_xlat10_2.xyz;
    }
    u_xlat16_3.xyz = u_xlat16_0.xyz * _Lut_Params.www;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_3.xyz = min(max(u_xlat16_3.xyz, 0.0), 1.0);
#else
    u_xlat16_3.xyz = clamp(u_xlat16_3.xyz, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb1.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb1.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb1.x){
        u_xlat16_4.xyz = u_xlat16_3.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_5.xyz = log2(u_xlat16_3.xyz);
        u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_5.xyz = exp2(u_xlat16_5.xyz);
        u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb1.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_3.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_4;
            hlslcc_movcTemp.x = (u_xlatb1.x) ? u_xlat16_4.x : u_xlat16_5.x;
            hlslcc_movcTemp.y = (u_xlatb1.y) ? u_xlat16_4.y : u_xlat16_5.y;
            hlslcc_movcTemp.z = (u_xlatb1.z) ? u_xlat16_4.z : u_xlat16_5.z;
            u_xlat16_4 = hlslcc_movcTemp;
        }
        u_xlat1.xyz = u_xlat16_4.zxy * _UserLut_Params.zzz;
        u_xlat1.x = floor(u_xlat1.x);
        u_xlat6.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat6.yz = u_xlat1.yz * _UserLut_Params.xy + u_xlat6.xy;
        u_xlat6.x = u_xlat1.x * _UserLut_Params.y + u_xlat6.y;
        u_xlat10_9.xyz = textureLod(_UserLut, u_xlat6.xz, 0.0).xyz;
        u_xlat7.x = _UserLut_Params.y;
        u_xlat7.y = 0.0;
        u_xlat6.xy = u_xlat6.xz + u_xlat7.xy;
        u_xlat10_6.xyz = textureLod(_UserLut, u_xlat6.xy, 0.0).xyz;
        u_xlat1.x = u_xlat16_4.z * _UserLut_Params.z + (-u_xlat1.x);
        u_xlat6.xyz = (-u_xlat10_9.xyz) + u_xlat10_6.xyz;
        u_xlat1.xyz = u_xlat1.xxx * u_xlat6.xyz + u_xlat10_9.xyz;
        u_xlat1.xyz = (-u_xlat16_4.xyz) + u_xlat1.xyz;
        u_xlat1.xyz = _UserLut_Params.www * u_xlat1.xyz + u_xlat16_4.xyz;
        u_xlat16_4.xyz = u_xlat1.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_5.xyz = u_xlat1.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_5.xyz = log2(abs(u_xlat16_5.xyz));
        u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_5.xyz = exp2(u_xlat16_5.xyz);
        u_xlatb1.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat1.xyzx).xyz;
        u_xlat16_3.x = (u_xlatb1.x) ? u_xlat16_4.x : u_xlat16_5.x;
        u_xlat16_3.y = (u_xlatb1.y) ? u_xlat16_4.y : u_xlat16_5.y;
        u_xlat16_3.z = (u_xlatb1.z) ? u_xlat16_4.z : u_xlat16_5.z;
    }
    u_xlat1.xyz = u_xlat16_3.zxy * _Lut_Params.zzz;
    u_xlat1.x = floor(u_xlat1.x);
    u_xlat6.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat6.yz = u_xlat1.yz * _Lut_Params.xy + u_xlat6.xy;
    u_xlat6.x = u_xlat1.x * _Lut_Params.y + u_xlat6.y;
    u_xlat10_9.xyz = textureLod(_InternalLut, u_xlat6.xz, 0.0).xyz;
    u_xlat7.x = _Lut_Params.y;
    u_xlat7.y = 0.0;
    u_xlat6.xy = u_xlat6.xz + u_xlat7.xy;
    u_xlat10_6.xyz = textureLod(_InternalLut, u_xlat6.xy, 0.0).xyz;
    u_xlat1.x = u_xlat16_3.z * _Lut_Params.z + (-u_xlat1.x);
    u_xlat6.xyz = (-u_xlat10_9.xyz) + u_xlat10_6.xyz;
    u_xlat1.xyz = u_xlat1.xxx * u_xlat6.xyz + u_xlat10_9.xyz;
    u_xlat6.xy = vs_TEXCOORD0.xy * _Dithering_Params.xy + _Dithering_Params.zw;
    u_xlat10_25 = texture(_BlueNoise_Texture, u_xlat6.xy).w;
    u_xlat25 = u_xlat10_25 * 2.0 + -1.0;
#ifdef UNITY_ADRENO_ES3
    u_xlatb6 = !!(u_xlat25>=0.0);
#else
    u_xlatb6 = u_xlat25>=0.0;
#endif
    u_xlat6.x = (u_xlatb6) ? 1.0 : -1.0;
    u_xlat25 = -abs(u_xlat25) + 1.0;
    u_xlat25 = sqrt(u_xlat25);
    u_xlat25 = (-u_xlat25) + 1.0;
    u_xlat25 = u_xlat25 * u_xlat6.x;
    u_xlat16_3.xyz = u_xlat1.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlat16_4.xyz = log2(abs(u_xlat1.xyz));
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_4.xyz = exp2(u_xlat16_4.xyz);
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlatb1.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat1.xyzx).xyz;
    {
        vec3 hlslcc_movcTemp = u_xlat16_3;
        hlslcc_movcTemp.x = (u_xlatb1.x) ? u_xlat16_3.x : u_xlat16_4.x;
        hlslcc_movcTemp.y = (u_xlatb1.y) ? u_xlat16_3.y : u_xlat16_4.y;
        hlslcc_movcTemp.z = (u_xlatb1.z) ? u_xlat16_3.z : u_xlat16_4.z;
        u_xlat16_3 = hlslcc_movcTemp;
    }
    u_xlat1.xyz = vec3(u_xlat25) * vec3(0.00392156886, 0.00392156886, 0.00392156886) + u_xlat16_3.xyz;
    u_xlat16_3.xyz = u_xlat1.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
    u_xlat16_4.xyz = u_xlat1.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
    u_xlat16_4.xyz = log2(abs(u_xlat16_4.xyz));
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
    u_xlat16_4.xyz = exp2(u_xlat16_4.xyz);
    u_xlatb1.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat1.xyzx).xyz;
    {
        vec3 hlslcc_movcTemp = u_xlat16_3;
        hlslcc_movcTemp.x = (u_xlatb1.x) ? u_xlat16_3.x : u_xlat16_4.x;
        hlslcc_movcTemp.y = (u_xlatb1.y) ? u_xlat16_3.y : u_xlat16_4.y;
        hlslcc_movcTemp.z = (u_xlatb1.z) ? u_xlat16_3.z : u_xlat16_4.z;
        u_xlat16_3 = hlslcc_movcTemp;
    }
    SV_Target0.xyz = max(u_xlat16_3.xyz, vec3(0.0, 0.0, 0.0));
    SV_Target0.w = 1.0;
    return;
}

#endif
                                 $Globals`         _Lut_Params                          _UserLut_Params                         _Chroma_Params                           _Vignette_Params1                     0      _Vignette_Params2                     @      _Dithering_Params                     P          $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      _InternalLut                _UserLut                _BlueNoise_Texture                  UnityStereoEyeIndices                  