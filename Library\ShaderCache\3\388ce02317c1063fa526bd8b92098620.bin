H	  <Q                      
   _DEPTH_MSAA_4       /  #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBiasRt;
in highp vec4 in_POSITION0;
in highp vec2 in_TEXCOORD0;
out highp vec2 vs_TEXCOORD0;
void main()
{
    gl_Position.y = in_POSITION0.y * _ScaleBiasRt.x;
    gl_Position.xz = in_POSITION0.xz;
    gl_Position.w = 1.0;
    vs_TEXCOORD0.xy = in_TEXCOORD0.xy;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _CameraDepthAttachment_TexelSize;
UNITY_LOCATION(0) uniform highp sampler2DMS _CameraDepthAttachment;
in highp vec2 vs_TEXCOORD0;
vec2 u_xlat0;
uvec4 u_xlatu0;
float u_xlat1;
float u_xlat2;
float u_xlat3;
void main()
{
    u_xlat0.xy = vs_TEXCOORD0.xy * _CameraDepthAttachment_TexelSize.zw;
    u_xlatu0.xy =  uvec2(ivec2(u_xlat0.xy));
    u_xlatu0.z = uint(uint(0u));
    u_xlatu0.w = uint(uint(0u));
    u_xlat1 = texelFetch(_CameraDepthAttachment, ivec2(u_xlatu0.xy), 0).x;
    u_xlat1 = max(u_xlat1, 0.0);
    u_xlat3 = texelFetch(_CameraDepthAttachment, ivec2(u_xlatu0.xy), 1).x;
    u_xlat1 = max(u_xlat1, u_xlat3);
    u_xlat3 = texelFetch(_CameraDepthAttachment, ivec2(u_xlatu0.xy), 2).x;
    u_xlat0.x = texelFetch(_CameraDepthAttachment, ivec2(u_xlatu0.xy), 3).x;
    u_xlat2 = max(u_xlat1, u_xlat3);
    gl_FragDepth = max(u_xlat2, u_xlat0.x);
    return;
}

#endif
                              $Globals          _CameraDepthAttachment_TexelSize                             $Globals         _ScaleBiasRt                                _CameraDepthAttachment                     