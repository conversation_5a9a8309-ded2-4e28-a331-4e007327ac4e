ScriptsOnlyBuild:
  usedScripts:
    Assembly-CSharp.dll:
    - IndustryMR.ComponentDestruction.ComponentData
    - IndustryMR.ComponentDestruction.LevelInfo
    - IndustryMR.ComponentDestruction.LevelUnit
    - IndustryMR.ComponentDestruction.MainMenu
    - IndustryMR.ComponentDestruction.NoneStirrupBeam.Controller
    - IndustryMR.ComponentDestruction.NoneStirrupBeam.FallRock
    - IndustryMR.ComponentDestruction.NoneStirrupBeam.Manager
    - IndustryMR.MainMenu
    - IndustryMR.PlayerController
    - SceneLoader
    - Starter
    - XimmerseTrigger
    DOTween.dll:
    - DG.Tweening.Core.DOTweenSettings
    Microsoft.MixedReality.GraphicsTools.dll:
    - Microsoft.MixedReality.GraphicsTools.CanvasElementRoundedRect
    - Microsoft.MixedReality.GraphicsTools.CanvasMaterialAnimatorCanvasFrontplate
    - Microsoft.MixedReality.GraphicsTools.CanvasMaterialAnimatorCanvasGlow
    - Microsoft.MixedReality.GraphicsTools.FrontPlatePulse
    - Microsoft.MixedReality.GraphicsTools.ScaleMeshEffect
    Microsoft.MixedReality.Toolkit.Core.dll:
    - Microsoft.MixedReality.Toolkit.MRTKProfile
    Microsoft.MixedReality.Toolkit.SpatialManipulation.dll:
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.BoundsControl
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.BoundsHandleInteractable
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.ConstraintManager
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.MinMaxScaleConstraint
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.ObjectManipulator
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.ScaleHandleInteractable
    - Microsoft.MixedReality.Toolkit.SpatialManipulation.SqueezableBoxVisuals
    Microsoft.MixedReality.Toolkit.UXCore.dll:
    - Microsoft.MixedReality.Toolkit.UX.Dialog
    - Microsoft.MixedReality.Toolkit.UX.DialogPool
    - Microsoft.MixedReality.Toolkit.UX.FontIconSelector
    - Microsoft.MixedReality.Toolkit.UX.FontIconSet
    - Microsoft.MixedReality.Toolkit.UX.InteractablePulse
    - Microsoft.MixedReality.Toolkit.UX.PressableButton
    - Microsoft.MixedReality.Toolkit.UX.RectTransformColliderFitter
    - Microsoft.MixedReality.Toolkit.UX.StateVisualizer
    - Microsoft.MixedReality.Toolkit.UX.UGUIInputAdapter
    - Microsoft.MixedReality.Toolkit.UX.UGUIInputAdapterDraggable
    RhinoX-Demo.dll:
    - Readme
    RhinoX-Interaction-System.dll:
    - Ximmerse.RhinoX.Billboard
    - Ximmerse.RhinoX.FrontDocker
    - Ximmerse.RhinoX.Outline
    - Ximmerse.RhinoX.PlayerHand
    - Ximmerse.RhinoX.PlayerHandAnimator
    - Ximmerse.RhinoX.RXDeviceAuthentication
    - Ximmerse.RhinoX.RXInteractionSystem
    - Ximmerse.RhinoX.RxAuthenticationEvent
    - Ximmerse.RhinoX.RxControllerAdaptor
    - Ximmerse.RhinoX.RxControllerView
    RhinoX-Unity.dll:
    - Ximmerse.RhinoX.ARCamera
    - Ximmerse.RhinoX.DynamicTarget
    - Ximmerse.RhinoX.GroundPlane
    - Ximmerse.RhinoX.Internal.AwaiterCoroutineer
    - Ximmerse.RhinoX.ObjectTrackingProfile
    - Ximmerse.RhinoX.RXButtonEventTrigger
    - Ximmerse.RhinoX.RXController
    - Ximmerse.RhinoX.RXEventSystem
    - Ximmerse.RhinoX.RXInputModule
    - Ximmerse.RhinoX.RhinoXGlobalSetting
    - Ximmerse.RhinoX.TrackableIdentity
    RhinoXDebugger.dll:
    - RhinoXDebugger.Asyncoroutine.AwaiterCoroutineer
    - RhinoXDebugger.DebuggerGlobalSetting
    Unity.Formats.Fbx.Editor.dll:
    - UnityEditor.Formats.Fbx.Exporter.ExportSettings
    Unity.InputSystem.dll:
    - UnityEngine.InputSystem.InputSettings
    - UnityEngine.InputSystem.InputSystemObject
    - UnityEngine.InputSystem.RemoteInputPlayerConnection
    Unity.Mobile.AndroidLogcat.Editor.dll:
    - Unity.Android.Logcat.AndroidLogcatConsoleWindow
    - Unity.Android.Logcat.AndroidLogcatManager
    Unity.ProBuilder.dll:
    - UnityEngine.ProBuilder.ProBuilderMesh
    Unity.RenderPipelines.Core.Runtime.dll:
    - UnityEngine.Rendering.UI.DebugUIHandlerBitField
    - UnityEngine.Rendering.UI.DebugUIHandlerButton
    - UnityEngine.Rendering.UI.DebugUIHandlerCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerColor
    - UnityEngine.Rendering.UI.DebugUIHandlerContainer
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumField
    - UnityEngine.Rendering.UI.DebugUIHandlerEnumHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerFoldout
    - UnityEngine.Rendering.UI.DebugUIHandlerGroup
    - UnityEngine.Rendering.UI.DebugUIHandlerHBox
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectFloatField
    - UnityEngine.Rendering.UI.DebugUIHandlerIndirectToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerPanel
    - UnityEngine.Rendering.UI.DebugUIHandlerPersistentCanvas
    - UnityEngine.Rendering.UI.DebugUIHandlerRow
    - UnityEngine.Rendering.UI.DebugUIHandlerToggle
    - UnityEngine.Rendering.UI.DebugUIHandlerToggleHistory
    - UnityEngine.Rendering.UI.DebugUIHandlerUIntField
    - UnityEngine.Rendering.UI.DebugUIHandlerVBox
    - UnityEngine.Rendering.UI.DebugUIHandlerValue
    - UnityEngine.Rendering.UI.DebugUIHandlerVector2
    - UnityEngine.Rendering.UI.DebugUIHandlerVector3
    - UnityEngine.Rendering.UI.DebugUIHandlerVector4
    - UnityEngine.Rendering.UI.UIFoldout
    - UnityEngine.Rendering.Volume
    - UnityEngine.Rendering.VolumeProfile
    Unity.RenderPipelines.Universal.Editor.dll:
    - UnityEditor.Rendering.Universal.UniversalProjectSettings
    Unity.RenderPipelines.Universal.Runtime.dll:
    - UnityEngine.Experimental.Rendering.Universal.RenderObjects
    - UnityEngine.Rendering.Universal.Bloom
    - UnityEngine.Rendering.Universal.ColorAdjustments
    - UnityEngine.Rendering.Universal.ForwardRendererData
    - UnityEngine.Rendering.Universal.PostProcessData
    - UnityEngine.Rendering.Universal.UniversalAdditionalCameraData
    - UnityEngine.Rendering.Universal.UniversalAdditionalLightData
    - UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset
    - UnityEngine.Rendering.Universal.XRSystemData
    Unity.Rider.Editor.dll:
    - Packages.Rider.Editor.RiderScriptEditorData
    - Packages.Rider.Editor.UnitTesting.CallbackData
    Unity.TextMeshPro.Editor.dll:
    - TMPro.EditorUtilities.TMPro_FontAssetCreatorWindow
    Unity.TextMeshPro.dll:
    - TMPro.TMP_FontAsset
    - TMPro.TMP_Settings
    - TMPro.TMP_SpriteAsset
    - TMPro.TMP_StyleSheet
    - TMPro.TextMeshPro
    - TMPro.TextMeshProUGUI
    Unity.XR.Interaction.Toolkit.Editor.dll:
    - UnityEditor.XR.Interaction.Toolkit.XRInteractionEditorSettings
    Unity.XR.Interaction.Toolkit.dll:
    - UnityEngine.XR.Interaction.Toolkit.ActionBasedController
    - UnityEngine.XR.Interaction.Toolkit.InteractionLayerSettings
    - UnityEngine.XR.Interaction.Toolkit.UI.TrackedDeviceGraphicRaycaster
    - UnityEngine.XR.Interaction.Toolkit.UI.XRUIInputModule
    - UnityEngine.XR.Interaction.Toolkit.XRInteractionManager
    - UnityEngine.XR.Interaction.Toolkit.XRInteractorLineVisual
    - UnityEngine.XR.Interaction.Toolkit.XRRayInteractor
    Unity.XR.Management.Editor.dll:
    - UnityEditor.XR.Management.XRGeneralSettingsPerBuildTarget
    Unity.XR.Management.dll:
    - UnityEngine.XR.Management.XRGeneralSettings
    - UnityEngine.XR.Management.XRManagerSettings
    UnityEditor.Graphs.dll:
    - UnityEditor.Graphs.AnimationBlendTree.Graph
    - UnityEditor.Graphs.AnimationBlendTree.GraphGUI
    - UnityEditor.Graphs.AnimationStateMachine.Graph
    - UnityEditor.Graphs.AnimationStateMachine.GraphGUI
    - UnityEditor.Graphs.AnimatorControllerTool
    UnityEditor.TestRunner.dll:
    - UnityEditor.TestTools.TestRunner.Api.CallbacksHolder
    - UnityEditor.TestTools.TestRunner.TestListCacheData
    - UnityEditor.TestTools.TestRunner.TestRun.TestJobDataHolder
    UnityEngine.UI.dll:
    - UnityEngine.EventSystems.EventSystem
    - UnityEngine.EventSystems.PhysicsRaycaster
    - UnityEngine.UI.Button
    - UnityEngine.UI.CanvasScaler
    - UnityEngine.UI.ContentSizeFitter
    - UnityEngine.UI.GraphicRaycaster
    - UnityEngine.UI.GridLayoutGroup
    - UnityEngine.UI.HorizontalLayoutGroup
    - UnityEngine.UI.Image
    - UnityEngine.UI.LayoutElement
    - UnityEngine.UI.Mask
    - UnityEngine.UI.RawImage
    - UnityEngine.UI.ScrollRect
    - UnityEngine.UI.Scrollbar
    - UnityEngine.UI.Selectable
    - UnityEngine.UI.Text
    - UnityEngine.UI.Toggle
    - UnityEngine.UI.VerticalLayoutGroup
  sceneClasses:
    Assets/Scenes/MainMenu.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1932}
    - Class: 114
      Script: {instanceID: 1934}
    - Class: 114
      Script: {instanceID: 1936}
    - Class: 114
      Script: {instanceID: 1942}
    - Class: 114
      Script: {instanceID: 1946}
    - Class: 114
      Script: {instanceID: 1950}
    - Class: 114
      Script: {instanceID: 1952}
    - Class: 114
      Script: {instanceID: 1954}
    - Class: 114
      Script: {instanceID: 2028}
    - Class: 114
      Script: {instanceID: 2352}
    - Class: 114
      Script: {instanceID: 3056}
    - Class: 114
      Script: {instanceID: 3652}
    - Class: 114
      Script: {instanceID: 3826}
    - Class: 114
      Script: {instanceID: 4654}
    - Class: 114
      Script: {instanceID: 4974}
    - Class: 114
      Script: {instanceID: 5484}
    - Class: 114
      Script: {instanceID: 5882}
    - Class: 114
      Script: {instanceID: 6020}
    - Class: 114
      Script: {instanceID: 6540}
    - Class: 114
      Script: {instanceID: 6670}
    - Class: 114
      Script: {instanceID: 6986}
    - Class: 114
      Script: {instanceID: 7264}
    - Class: 114
      Script: {instanceID: 7914}
    - Class: 114
      Script: {instanceID: 8228}
    - Class: 114
      Script: {instanceID: 8386}
    - Class: 114
      Script: {instanceID: 8916}
    - Class: 114
      Script: {instanceID: 9464}
    - Class: 114
      Script: {instanceID: 10352}
    - Class: 114
      Script: {instanceID: 11002}
    - Class: 114
      Script: {instanceID: 11210}
    - Class: 114
      Script: {instanceID: 11338}
    - Class: 114
      Script: {instanceID: 11430}
    - Class: 114
      Script: {instanceID: 11678}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 258
      Script: {instanceID: 0}
    Assets/Start.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 49
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 82
      Script: {instanceID: 0}
    - Class: 83
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 102
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1930}
    - Class: 114
      Script: {instanceID: 1932}
    - Class: 114
      Script: {instanceID: 1934}
    - Class: 114
      Script: {instanceID: 1936}
    - Class: 114
      Script: {instanceID: 1938}
    - Class: 114
      Script: {instanceID: 1940}
    - Class: 114
      Script: {instanceID: 1942}
    - Class: 114
      Script: {instanceID: 1950}
    - Class: 114
      Script: {instanceID: 1952}
    - Class: 114
      Script: {instanceID: 1954}
    - Class: 114
      Script: {instanceID: 1960}
    - Class: 114
      Script: {instanceID: 1962}
    - Class: 114
      Script: {instanceID: 1978}
    - Class: 114
      Script: {instanceID: 2028}
    - Class: 114
      Script: {instanceID: 2342}
    - Class: 114
      Script: {instanceID: 2410}
    - Class: 114
      Script: {instanceID: 2850}
    - Class: 114
      Script: {instanceID: 3056}
    - Class: 114
      Script: {instanceID: 3080}
    - Class: 114
      Script: {instanceID: 3104}
    - Class: 114
      Script: {instanceID: 3222}
    - Class: 114
      Script: {instanceID: 3552}
    - Class: 114
      Script: {instanceID: 3798}
    - Class: 114
      Script: {instanceID: 3810}
    - Class: 114
      Script: {instanceID: 3826}
    - Class: 114
      Script: {instanceID: 4374}
    - Class: 114
      Script: {instanceID: 4472}
    - Class: 114
      Script: {instanceID: 4606}
    - Class: 114
      Script: {instanceID: 4650}
    - Class: 114
      Script: {instanceID: 4788}
    - Class: 114
      Script: {instanceID: 4958}
    - Class: 114
      Script: {instanceID: 4974}
    - Class: 114
      Script: {instanceID: 5120}
    - Class: 114
      Script: {instanceID: 5228}
    - Class: 114
      Script: {instanceID: 5374}
    - Class: 114
      Script: {instanceID: 5556}
    - Class: 114
      Script: {instanceID: 5750}
    - Class: 114
      Script: {instanceID: 5882}
    - Class: 114
      Script: {instanceID: 5986}
    - Class: 114
      Script: {instanceID: 6128}
    - Class: 114
      Script: {instanceID: 6290}
    - Class: 114
      Script: {instanceID: 6740}
    - Class: 114
      Script: {instanceID: 6868}
    - Class: 114
      Script: {instanceID: 6986}
    - Class: 114
      Script: {instanceID: 7198}
    - Class: 114
      Script: {instanceID: 7264}
    - Class: 114
      Script: {instanceID: 7284}
    - Class: 114
      Script: {instanceID: 7584}
    - Class: 114
      Script: {instanceID: 7586}
    - Class: 114
      Script: {instanceID: 7608}
    - Class: 114
      Script: {instanceID: 7914}
    - Class: 114
      Script: {instanceID: 7944}
    - Class: 114
      Script: {instanceID: 7960}
    - Class: 114
      Script: {instanceID: 8012}
    - Class: 114
      Script: {instanceID: 8228}
    - Class: 114
      Script: {instanceID: 8266}
    - Class: 114
      Script: {instanceID: 8376}
    - Class: 114
      Script: {instanceID: 8386}
    - Class: 114
      Script: {instanceID: 8492}
    - Class: 114
      Script: {instanceID: 8494}
    - Class: 114
      Script: {instanceID: 8500}
    - Class: 114
      Script: {instanceID: 8556}
    - Class: 114
      Script: {instanceID: 8560}
    - Class: 114
      Script: {instanceID: 8564}
    - Class: 114
      Script: {instanceID: 8568}
    - Class: 114
      Script: {instanceID: 8580}
    - Class: 114
      Script: {instanceID: 8582}
    - Class: 114
      Script: {instanceID: 8588}
    - Class: 114
      Script: {instanceID: 8590}
    - Class: 114
      Script: {instanceID: 8722}
    - Class: 114
      Script: {instanceID: 8778}
    - Class: 114
      Script: {instanceID: 8838}
    - Class: 114
      Script: {instanceID: 8840}
    - Class: 114
      Script: {instanceID: 8958}
    - Class: 114
      Script: {instanceID: 9102}
    - Class: 114
      Script: {instanceID: 9198}
    - Class: 114
      Script: {instanceID: 9336}
    - Class: 114
      Script: {instanceID: 9464}
    - Class: 114
      Script: {instanceID: 9520}
    - Class: 114
      Script: {instanceID: 9624}
    - Class: 114
      Script: {instanceID: 11002}
    - Class: 114
      Script: {instanceID: 11052}
    - Class: 114
      Script: {instanceID: 11210}
    - Class: 114
      Script: {instanceID: 11430}
    - Class: 114
      Script: {instanceID: 11458}
    - Class: 114
      Script: {instanceID: 11678}
    - Class: 114
      Script: {instanceID: 11742}
    - Class: 114
      Script: {instanceID: 53246}
    - Class: 114
      Script: {instanceID: 53250}
    - Class: 114
      Script: {instanceID: 53254}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 198
      Script: {instanceID: 0}
    - Class: 199
      Script: {instanceID: 0}
    - Class: 210
      Script: {instanceID: 0}
    - Class: 212
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    Assets/_Project/ComponentDestruction/Scenes/ComponentDestruction_MainMenu.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1932}
    - Class: 114
      Script: {instanceID: 1934}
    - Class: 114
      Script: {instanceID: 1936}
    - Class: 114
      Script: {instanceID: 1942}
    - Class: 114
      Script: {instanceID: 1952}
    - Class: 114
      Script: {instanceID: 1954}
    - Class: 114
      Script: {instanceID: 1962}
    - Class: 114
      Script: {instanceID: 2222}
    - Class: 114
      Script: {instanceID: 2696}
    - Class: 114
      Script: {instanceID: 3340}
    - Class: 114
      Script: {instanceID: 3396}
    - Class: 114
      Script: {instanceID: 6290}
    - Class: 114
      Script: {instanceID: 7264}
    - Class: 114
      Script: {instanceID: 8386}
    - Class: 114
      Script: {instanceID: 8564}
    - Class: 114
      Script: {instanceID: 9582}
    - Class: 114
      Script: {instanceID: 11338}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    Assets/_Project/ComponentDestruction/Scenes/NoneStirrupBeam.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 20
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 54
      Script: {instanceID: 0}
    - Class: 64
      Script: {instanceID: 0}
    - Class: 65
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 81
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 108
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1932}
    - Class: 114
      Script: {instanceID: 1934}
    - Class: 114
      Script: {instanceID: 1936}
    - Class: 114
      Script: {instanceID: 1954}
    - Class: 114
      Script: {instanceID: 1960}
    - Class: 114
      Script: {instanceID: 1962}
    - Class: 114
      Script: {instanceID: 2010}
    - Class: 114
      Script: {instanceID: 2246}
    - Class: 114
      Script: {instanceID: 3508}
    - Class: 114
      Script: {instanceID: 4374}
    - Class: 114
      Script: {instanceID: 4684}
    - Class: 114
      Script: {instanceID: 4882}
    - Class: 114
      Script: {instanceID: 5104}
    - Class: 114
      Script: {instanceID: 5672}
    - Class: 114
      Script: {instanceID: 5802}
    - Class: 114
      Script: {instanceID: 6290}
    - Class: 114
      Script: {instanceID: 6292}
    - Class: 114
      Script: {instanceID: 6688}
    - Class: 114
      Script: {instanceID: 6936}
    - Class: 114
      Script: {instanceID: 7264}
    - Class: 114
      Script: {instanceID: 8180}
    - Class: 114
      Script: {instanceID: 8386}
    - Class: 114
      Script: {instanceID: 8556}
    - Class: 114
      Script: {instanceID: 8560}
    - Class: 114
      Script: {instanceID: 8564}
    - Class: 114
      Script: {instanceID: 8568}
    - Class: 114
      Script: {instanceID: 8588}
    - Class: 114
      Script: {instanceID: 8590}
    - Class: 114
      Script: {instanceID: 9428}
    - Class: 114
      Script: {instanceID: 9582}
    - Class: 114
      Script: {instanceID: 9624}
    - Class: 114
      Script: {instanceID: 10502}
    - Class: 114
      Script: {instanceID: 10750}
    - Class: 114
      Script: {instanceID: 11152}
    - Class: 114
      Script: {instanceID: 11234}
    - Class: 114
      Script: {instanceID: 11292}
    - Class: 114
      Script: {instanceID: 11338}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 120
      Script: {instanceID: 0}
    - Class: 128
      Script: {instanceID: 0}
    - Class: 136
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 213
      Script: {instanceID: 0}
    - Class: 220
      Script: {instanceID: 0}
    - Class: 221
      Script: {instanceID: 0}
    - Class: 222
      Script: {instanceID: 0}
    - Class: 223
      Script: {instanceID: 0}
    - Class: 224
      Script: {instanceID: 0}
    - Class: 225
      Script: {instanceID: 0}
    - Class: 258
      Script: {instanceID: 0}
    - Class: 850595691
      Script: {instanceID: 0}
    Assets/_Project/_01_HighAlt/Scenes/_HighAlt_ObjectDrop.unity:
    - Class: 1
      Script: {instanceID: 0}
    - Class: 4
      Script: {instanceID: 0}
    - Class: 21
      Script: {instanceID: 0}
    - Class: 23
      Script: {instanceID: 0}
    - Class: 28
      Script: {instanceID: 0}
    - Class: 33
      Script: {instanceID: 0}
    - Class: 43
      Script: {instanceID: 0}
    - Class: 48
      Script: {instanceID: 0}
    - Class: 74
      Script: {instanceID: 0}
    - Class: 89
      Script: {instanceID: 0}
    - Class: 90
      Script: {instanceID: 0}
    - Class: 91
      Script: {instanceID: 0}
    - Class: 95
      Script: {instanceID: 0}
    - Class: 104
      Script: {instanceID: 0}
    - Class: 114
      Script: {instanceID: 1962}
    - Class: 114
      Script: {instanceID: 8564}
    - Class: 115
      Script: {instanceID: 0}
    - Class: 137
      Script: {instanceID: 0}
    - Class: 157
      Script: {instanceID: 0}
    - Class: 196
      Script: {instanceID: 0}
    - Class: 258
      Script: {instanceID: 0}
  scriptHashData:
  - hash:
      serializedVersion: 2
      Hash: aba7b2852ef61d175dd9602e7723d5e5
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Mask
  - hash:
      serializedVersion: 2
      Hash: 3164bc883ff663a5cdb32d1ef7e7c0c5
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: HorizontalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 69415240821be7986b27e72bf9609a30
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Button
  - hash:
      serializedVersion: 2
      Hash: a6e561615949734e9f12f9b61661f43e
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Image
  - hash:
      serializedVersion: 2
      Hash: 040dd48e3a027d5424519a5fa2009df4
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ScrollRect
  - hash:
      serializedVersion: 2
      Hash: bcaee4c2a83047257b5f47d6ed264b75
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Toggle
  - hash:
      serializedVersion: 2
      Hash: 0dbc3be398b4869a5c8d0d8481ee402c
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: LayoutElement
  - hash:
      serializedVersion: 2
      Hash: 21a9c6dbf300fc1f6dc15ca7e2c39a37
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 7ab0e790220bdca831fdd886b339039d
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GridLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: d251bf88db6c95a6b1aa0b69298da9ef
    assemblyName: UnityEngine.SpatialTracking.dll
    namespaceName: UnityEngine.SpatialTracking
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 699619b86d9359741a4f58540298c80b
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Text
  - hash:
      serializedVersion: 2
      Hash: 8aa3d6ccbcb54ee8db6c1229854adaaa
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ContentSizeFitter
  - hash:
      serializedVersion: 2
      Hash: a1aa98c78061c6f03a8c124cda576e67
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: CanvasScaler
  - hash:
      serializedVersion: 2
      Hash: c5503f851ba144bfb96016f2016c1c6c
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: RXController
  - hash:
      serializedVersion: 2
      Hash: edbdb1dc215a2dfca758f6bbd013b5f5
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: TrackableIdentity
  - hash:
      serializedVersion: 2
      Hash: f69dfc6409fbbd89d3689368e99688a8
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector2
  - hash:
      serializedVersion: 2
      Hash: 9ac7c09bc01fda65a7a13c0f31daae9e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TwoHandedGrabMoveProvider
  - hash:
      serializedVersion: 2
      Hash: aa8b4a133fd0d8d77bedae7a6f5181f5
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: bae01a8e782816baed9bde1d3727a4ca
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: FontIconSelector
  - hash:
      serializedVersion: 2
      Hash: c24a778d9aa393e11e54bff320ca63de
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: PlayerInput
  - hash:
      serializedVersion: 2
      Hash: a56933f2ad2067fd2e0a2086c877ac3d
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: InteractableAnimatorVisuals
  - hash:
      serializedVersion: 2
      Hash: 3404ee9690ebdf5476c6f094d35ba914
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: InBetween
  - hash:
      serializedVersion: 2
      Hash: ef5c43f921427d03730f6ea4c848f07d
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: RectMask2DFast
  - hash:
      serializedVersion: 2
      Hash: 04e4a8b0447477cc4e933d9e5311188f
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: ArmModel
  - hash:
      serializedVersion: 2
      Hash: 0e2a54c7c89e991a8596ecab7d371d7c
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: SimpleHit
  - hash:
      serializedVersion: 2
      Hash: 3311df2c9196453241ceaa669a78f214
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ClippingSphere
  - hash:
      serializedVersion: 2
      Hash: ca0b8ad3987cd33b3209a508b6e1562e
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: FreeCamera
  - hash:
      serializedVersion: 2
      Hash: 87e6cfc8015337aef5fa238bd355b16f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRControllerRecorder
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalAsset
  - hash:
      serializedVersion: 2
      Hash: 7aac0b898f5aa960e7a740fd0da5b82e
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ScreenSpaceAmbientOcclusion
  - hash:
      serializedVersion: 2
      Hash: 6bc76e17f0d5e93f3eea7f5a2fe227f3
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Selectable
  - hash:
      serializedVersion: 2
      Hash: ba764536bbfa9e2e9aeb9de6171b6e0a
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: PlayerHand
  - hash:
      serializedVersion: 2
      Hash: ffb7a2f1ff1e227f3c4e7e8271f651c7
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: MousePointTriggerEvent
  - hash:
      serializedVersion: 2
      Hash: f799115847d439a9a95ec006092feaae
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: StatefulInteractable
  - hash:
      serializedVersion: 2
      Hash: a2d7453959d5d6c08b797ee0e3f612b8
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: AcrylicBackgroundRectProvider
  - hash:
      serializedVersion: 2
      Hash: 9b06583966b5c25325233c4ef1ab1fd4
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.HID.Editor
    className: HIDDescriptorWindow
  - hash:
      serializedVersion: 2
      Hash: 29cb5ccadda8ab62d8dba6d24d61faa4
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: VirtualMouseInput
  - hash:
      serializedVersion: 2
      Hash: caa533b001ae39b864ba8425019e2268
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Shadow
  - hash:
      serializedVersion: 2
      Hash: 9d10d16beb13fa2d89c4cfb99a4aa4b9
    assemblyName: Highlighters.dll
    namespaceName: Highlighters
    className: HighlighterTrigger
  - hash:
      serializedVersion: 2
      Hash: d41ad2caeeebca45b79c52b215d83f9c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedController
  - hash:
      serializedVersion: 2
      Hash: 69e9f3e2c0050519390edbd27a650352
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: RadialView
  - hash:
      serializedVersion: 2
      Hash: 7a7f9f5f58e21d02d71461ddf3f1e2b1
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: BoundsHandleInteractable
  - hash:
      serializedVersion: 2
      Hash: c5bfd0f6f4664318b8b9f50d88408421
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: DepthOfField
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRLegacyGrabTransformer
  - hash:
      serializedVersion: 2
      Hash: 9beeec04400a3c97e972328a48af98ae
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractorLineVisual
  - hash:
      serializedVersion: 2
      Hash: 2facb3d1fe7446b845afb0160a1c4dd8
    assemblyName: HighlightersUrp.dll
    namespaceName: Highlighters_URP
    className: HighlightsManagerURP
  - hash:
      serializedVersion: 2
      Hash: 9f0d2156cb2876cc856eb040bfe23e76
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: CompositeShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: efc9941449b377190bd9fa6c87b4f036
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorGraphicsToolsStandardCanvas
  - hash:
      serializedVersion: 2
      Hash: 8b8b18ce5ce466450710494b38db41e5
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: WhiteBalance
  - hash:
      serializedVersion: 2
      Hash: 2609a0c7f9892333a57d56c984b05355
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: CameraManager
  - hash:
      serializedVersion: 2
      Hash: b1abc553d2df7d97f8b84ea38cdb1e9d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TeleportationArea
  - hash:
      serializedVersion: 2
      Hash: 203390faa3ab533e8d366d969502a4fa
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction
    className: MainMenu
  - hash:
      serializedVersion: 2
      Hash: 6da05449502c54a7bf3cc65b493806ee
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: RenderObjects
  - hash:
      serializedVersion: 2
      Hash: 46bb4031913b885748fd5f96156f3707
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: SurfaceMagnetism
  - hash:
      serializedVersion: 2
      Hash: f0cd5ee7f0608db6afb77aadf97abcb7
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: SliderSounds
  - hash:
      serializedVersion: 2
      Hash: 2b94f31539988287b5d8b7060e36d9fe
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: StatefulInteractableSwitchToggleVisuals
  - hash:
      serializedVersion: 2
      Hash: fd49fec0ebe755490ecb8ed75c1448f5
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ClearRenderTarget
  - hash:
      serializedVersion: 2
      Hash: 800c50acba124432abb4cf0d47202e06
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_StyleSheet
  - hash:
      serializedVersion: 2
      Hash: 57a371edcae768b8b1a9afeb04f27af5
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: SliderVisuals
  - hash:
      serializedVersion: 2
      Hash: cd0ecdca0166232906c6f2df9dddc3d3
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: AdvancedDropdownWindow
  - hash:
      serializedVersion: 2
      Hash: 4de78781075c75589cdbaad292fb6cff
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner
    className: PlaymodeTestsController
  - hash:
      serializedVersion: 2
      Hash: 547e6917a4d34a1dc5d95fc9ceb66e62
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: HighlightersAPITest
  - hash:
      serializedVersion: 2
      Hash: f88e8151832ea97341730f1101a07fd6
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TeleportationAnchor
  - hash:
      serializedVersion: 2
      Hash: 46c9d79940426b49c36eef6ca33ed8e1
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: DirectionalIndicator
  - hash:
      serializedVersion: 2
      Hash: c1d728126ff1918728036c59106d32b2
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RawImage
  - hash:
      serializedVersion: 2
      Hash: 5e1eefcd5106de6c2e85908a3b2d3f04
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggle
  - hash:
      serializedVersion: 2
      Hash: 7a8cd50abd37c38cdb4e61496bcbd368
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: RXDeviceAuthentication
  - hash:
      serializedVersion: 2
      Hash: 95e6797232b7d5831c9feb55a8582905
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: SplitToning
  - hash:
      serializedVersion: 2
      Hash: 7ddd1e7ac00b055aa482a6db6bd23db2
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: UXBindingProfileTemplate
  - hash:
      serializedVersion: 2
      Hash: 841f439a20b4a381d5a0c5ec9e36deda
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorLookup
  - hash:
      serializedVersion: 2
      Hash: 51b05e403ce1da1805851f42af02f4bb
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerGroup
  - hash:
      serializedVersion: 2
      Hash: 26536c53ffc57c32da1333cbd900c118
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: ToggleCollection
  - hash:
      serializedVersion: 2
      Hash: 8afd0435cb508da5e2cae86937da9045
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRTintInteractableVisual
  - hash:
      serializedVersion: 2
      Hash: 25c37862fa9cb15481d8d37eb9f2bb77
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: UXBindingConfigurator
  - hash:
      serializedVersion: 2
      Hash: c13893a71aabe576b4391a22369aa786
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TunnelingVignetteController
  - hash:
      serializedVersion: 2
      Hash: 5b2330da860cd1fc7110dfd2c3c1535f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRRig
  - hash:
      serializedVersion: 2
      Hash: 31995eb8416877c9afd92b5d1b50df0d
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: XRSystemData
  - hash:
      serializedVersion: 2
      Hash: 35a562ba62bbe860f042c8765bf1116a
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction
    className: LevelUnit
  - hash:
      serializedVersion: 2
      Hash: ee01c3d6945597a096ed796f8131c945
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasShadow
  - hash:
      serializedVersion: 2
      Hash: 351af1a1422404e1f45ade8893827885
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction
    className: LevelInfo
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: AutoAddInputModules
  - hash:
      serializedVersion: 2
      Hash: dcec20f6a3e6efef3adeaab74ed5126f
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX.Deprecated
    className: DialogButton
  - hash:
      serializedVersion: 2
      Hash: 7a349000ce7f2d6a564098027564f8e9
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction
    className: ComponentData
  - hash:
      serializedVersion: 2
      Hash: cc4274b586df3c02688bac01b88bd16c
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventSystem
  - hash:
      serializedVersion: 2
      Hash: fe64a45214940b510a0ec79c96d43498
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Dropdown
  - hash:
      serializedVersion: 2
      Hash: f336e086360abe49a3323e5f1583c35a
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioTrack
  - hash:
      serializedVersion: 2
      Hash: c822eec45bd7c5f6d7c8a2d82a6feb07
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: TimelineAsset
  - hash:
      serializedVersion: 2
      Hash: 8ea91ba86692d8e81d2f08037d03d89a
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: SqueezableBoxVisuals
  - hash:
      serializedVersion: 2
      Hash: 78b8c2d095378c366de70f1eabb19654
    assemblyName: Assembly-CSharp.dll
    namespaceName: UnityTemplateProjects
    className: SimpleCameraController
  - hash:
      serializedVersion: 2
      Hash: f7b138083d0a0218ac6acbbb940de3d7
    assemblyName: Assembly-CSharp.dll
    namespaceName: LayerLab
    className: PanelSciFiSurvival
  - hash:
      serializedVersion: 2
      Hash: 802ea25fac59ef7a5dfb7eb06305cbf5
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: FixedRotationToWorldConstraint
  - hash:
      serializedVersion: 2
      Hash: 1f0602e7bad1e0a9cba31ce292eee8bb
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerWidget
  - hash:
      serializedVersion: 2
      Hash: 60c0bfb4f27fa9be3a8a1a4f5c4341d8
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ThemeCollection
  - hash:
      serializedVersion: 2
      Hash: d6a8e9bec3160a152b9b2710593d3caa
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ColorPalette
  - hash:
      serializedVersion: 2
      Hash: c48f123600a52036c112df3de200f058
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: KeyboardPreview
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Starter
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestRunner.Utils
    className: TestRunCallbackListener
  - hash:
      serializedVersion: 2
      Hash: ae83e830e7a90c0aff918c4c2e520825
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: GraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: b5c6e8ebeb45db62242b1abb0f53b3f0
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: UGUIInputAdapter
  - hash:
      serializedVersion: 2
      Hash: 5255ee91a5d932233c7f0b7fb495f85e
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: GazeAtLocator
  - hash:
      serializedVersion: 2
      Hash: f40fedccd4adbc76605bea6d67537e4b
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SelectionCaret
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils
    className: OnDestroyNotifier
  - hash:
      serializedVersion: 2
      Hash: a306e12d896ddcca949923c805691027
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: InputField
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAnimator
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: TestResultRendererCallback
  - hash:
      serializedVersion: 2
      Hash: 7025bc0083a38ba9c436e01fd88e2692
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: TapToPlace
  - hash:
      serializedVersion: 2
      Hash: ff00782dfe9743f1c03999fe3aa2ac2f
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorGraphicsToolsStandard
  - hash:
      serializedVersion: 2
      Hash: 2cfa0393b5123bd14aa7b550fd8912e4
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: ShadowCaster2D
  - hash:
      serializedVersion: 2
      Hash: 885d5488c340aa2bab56c801fd1741e5
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: VirtualizedScrollRectList
  - hash:
      serializedVersion: 2
      Hash: 32f6d1d5a09e5c94c633a32edd3e6836
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: BezierShape
  - hash:
      serializedVersion: 2
      Hash: 5d5b0c61f3ab6ff5f41b1bbe19764526
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenButton
  - hash:
      serializedVersion: 2
      Hash: 3f0e2dacb7626b1f062c28b370a3512b
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedSnapTurnProvider
  - hash:
      serializedVersion: 2
      Hash: 03ce32152f42301bf845cb75dc08d535
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: MoveAxisConstraint
  - hash:
      serializedVersion: 2
      Hash: 0fd6822f6177297bd0eb4d83cc7a9030
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: ToggleGroup
  - hash:
      serializedVersion: 2
      Hash: aca7af8fe7d69be6ab50e3184d28e519
    assemblyName: Unity.Formats.Fbx.Runtime.dll
    namespaceName: UnityEngine.Formats.Fbx.Exporter
    className: FbxPrefab
  - hash:
      serializedVersion: 2
      Hash: 185b457f2d52afc3ef98d0c37d841af5
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalCameraData
  - hash:
      serializedVersion: 2
      Hash: 79c12b45755c7e37d526d34ab8285209
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: TriggerAPITest
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumField
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionImporterEditor
  - hash:
      serializedVersion: 2
      Hash: 059e1cc65b65ed078277b87abf4e3246
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: AcrylicHelper
  - hash:
      serializedVersion: 2
      Hash: 2388eb9e99b86b3813e7b6dbfe4b3e0c
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerContainer
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: RxControllerView
  - hash:
      serializedVersion: 2
      Hash: fe5ff6f4678876d628fccea49e667812
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: MinMaxScaleConstraint
  - hash:
      serializedVersion: 2
      Hash: 85acc6ab8188eb659e312d2784be6455
    assemblyName: RhinoX-Demo.dll
    namespaceName: 
    className: Readme
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayerQuitHandler
  - hash:
      serializedVersion: 2
      Hash: 5ab84e0a59bcfc44f634a7d3ca5df7bf
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: MeshSmoother
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: BaseInput
  - hash:
      serializedVersion: 2
      Hash: 0e2a54c7c89e991a8596ecab7d371d7c
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: ChomperHit
  - hash:
      serializedVersion: 2
      Hash: 7fe3bf96cc539b23b75082216600d89a
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRGrabInteractable
  - hash:
      serializedVersion: 2
      Hash: 1f0602e7bad1e0a9cba31ce292eee8bb
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVBox
  - hash:
      serializedVersion: 2
      Hash: 9a856118d10ab925bf0ead0343c8f76b
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: FlyCameraController
  - hash:
      serializedVersion: 2
      Hash: 7dd9f247bf3f74780559d46bac892a95
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Filtering
    className: XRTargetFilter
  - hash:
      serializedVersion: 2
      Hash: 3f8de4015b88591541365748fe8f7412
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineAsset
  - hash:
      serializedVersion: 2
      Hash: 98aa0a0c8ff2910eb12af89c7e0eb661
    assemblyName: Unity.XR.Management.dll
    namespaceName: UnityEngine.XR.Management
    className: XRGeneralSettings
  - hash:
      serializedVersion: 2
      Hash: da4cc51a02ce84afe2cecd27cfe6997a
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshPro
  - hash:
      serializedVersion: 2
      Hash: bd3cf68c8964d3c66d1c43f4f98508c9
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationTrack
  - hash:
      serializedVersion: 2
      Hash: b854e1a8a79400bce74b5cd76eef3a39
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: SolverHandler
  - hash:
      serializedVersion: 2
      Hash: 8ab9295d94483c961936d82d233d3df2
    assemblyName: Unity.XR.Management.dll
    namespaceName: UnityEngine.XR.Management
    className: XRManagerSettings
  - hash:
      serializedVersion: 2
      Hash: 5e1eefcd5106de6c2e85908a3b2d3f04
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerToggleHistory
  - hash:
      serializedVersion: 2
      Hash: d8ae79fcd22b9d19b3bcfc9490f1eaf7
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasElementRoundedRect
  - hash:
      serializedVersion: 2
      Hash: 6f29b049d6b6b18bee23aaf2fe273e02
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorAdjustments
  - hash:
      serializedVersion: 2
      Hash: c24b0c4ed62d8b7eabb5a4c9a20f4e00
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: InteractionLayerSettings
  - hash:
      serializedVersion: 2
      Hash: d79be48f820686024dd0d9e080d7733b
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputActionAsset
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: glTFast.dll
    namespaceName: GLTFast
    className: UninterruptedDefaultDeferAgent
  - hash:
      serializedVersion: 2
      Hash: a5781dc71a671b1ad2b38de19f24b71f
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationTrack
  - hash:
      serializedVersion: 2
      Hash: 095be22098d0d0e8e797ac4834a873cd
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: ObjectBar
  - hash:
      serializedVersion: 2
      Hash: f6633ace6e530c0000f2ac6da4269c52
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: PolyShape
  - hash:
      serializedVersion: 2
      Hash: 4efe5f564d36c37295fd0e7407c4ad9a
    assemblyName: Unity.XR.CoreUtils.dll
    namespaceName: Unity.XR.CoreUtils
    className: XROrigin
  - hash:
      serializedVersion: 2
      Hash: 7968ebe382edce04909b91cd104a959d
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: DialogPool
  - hash:
      serializedVersion: 2
      Hash: cb85dfbfe1201b1363f4f3bec048656f
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorGraphicsToolsTextMeshPro
  - hash:
      serializedVersion: 2
      Hash: d58059b29afc9efdd4f38e035a1ea50d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: TeleportationProvider
  - hash:
      serializedVersion: 2
      Hash: 7c548788d27a46eb9e45322ff2098d7b
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractionManager
  - hash:
      serializedVersion: 2
      Hash: cd0ecdca0166232906c6f2df9dddc3d3
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: bfeb96683d3796cadaab890bfdfe92be
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: b07ee130ffa61a4b9b2935c6323730b0
    assemblyName: glTFast.dll
    namespaceName: GLTFast
    className: GltfBoundsAsset
  - hash:
      serializedVersion: 2
      Hash: 2118c016ea393705f2ded7e913a336c6
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: ObjectManipulator
  - hash:
      serializedVersion: 2
      Hash: 9ac327467393cb6c8045a2fd90ff67e2
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPersistentCanvas
  - hash:
      serializedVersion: 2
      Hash: ad4af01eeb7f0a9ae0be2df3e0f1953a
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputStateWindow
  - hash:
      serializedVersion: 2
      Hash: 50d893c48b1c02a4b8ede5fe28ff9e85
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: TrackedDeviceRaycaster
  - hash:
      serializedVersion: 2
      Hash: 8121b38f3e880c8309806af09d9476be
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: StateVisualAnimationAsset
  - hash:
      serializedVersion: 2
      Hash: 3165968ba45c74472d279ba04455fccb
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ColorGradient
  - hash:
      serializedVersion: 2
      Hash: 908b3077da284d11de0450423ca1808e
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalReceiver
  - hash:
      serializedVersion: 2
      Hash: 597cfde2e60b842b3acbd2961ff0631b
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ShadowsMidtonesHighlights
  - hash:
      serializedVersion: 2
      Hash: a306e12d896ddcca949923c805691027
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: MRTKUGUIInputField
  - hash:
      serializedVersion: 2
      Hash: 18c222bef9ae745ce69ddde9b74337ca
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: PhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 67a99c72bf51722a004979b57db87178
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: MotionBlur
  - hash:
      serializedVersion: 2
      Hash: 0fb27be807aa299702f28ad0bcad1f46
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: ef5c43f921427d03730f6ea4c848f07d
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: RectMask2D
  - hash:
      serializedVersion: 2
      Hash: cd1e91f5afed259a1512bbf18dc12bd7
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: 
    className: SceneRenderPipeline
  - hash:
      serializedVersion: 2
      Hash: 87ad3705a58f19c47136728d4a6e9c47
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_Settings
  - hash:
      serializedVersion: 2
      Hash: d8c9c53ba0b1477ec5a91355304f709c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: LocomotionSystem
  - hash:
      serializedVersion: 2
      Hash: 1db0a631a8c6c4c547c4838f562eead3
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: Bloom
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: glTFast.dll
    namespaceName: GLTFast
    className: DefaultDeferAgent
  - hash:
      serializedVersion: 2
      Hash: bdb2aa563ff34cd96dfe8e46b3382012
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ChromaticAberration
  - hash:
      serializedVersion: 2
      Hash: 98c977cd4fe7fc627c33039392dfaf19
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: PressableButton
  - hash:
      serializedVersion: 2
      Hash: 86ba9a8805ac7e04d0f500732865d9f3
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: DeviceBasedContinuousMoveProvider
  - hash:
      serializedVersion: 2
      Hash: 91c66736e3507bbd3c15a9b02abea21b
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: LensDistortion
  - hash:
      serializedVersion: 2
      Hash: 28001aa31fd3c7ece1813fedb97b4140
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: ControllerLookup
  - hash:
      serializedVersion: 2
      Hash: 1491903b781f7b1c40c6d4aa1271da1a
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: Renderer2DData
  - hash:
      serializedVersion: 2
      Hash: 6980e81ddc615a861309d2d659cc3760
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMeshUI
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerEnumHistory
  - hash:
      serializedVersion: 2
      Hash: 56578e9243f73ca32099dd8b41c7f28c
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: ConstraintManager
  - hash:
      serializedVersion: 2
      Hash: 426e5bd616852254b77531cd1a8e29b6
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.OnScreen
    className: OnScreenStick
  - hash:
      serializedVersion: 2
      Hash: fd68d84f33fad85aac38f2739c0ee270
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SpriteAsset
  - hash:
      serializedVersion: 2
      Hash: 5481c34a98cd6db0ce3616d9855e8ba2
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: TestSceneManager
  - hash:
      serializedVersion: 2
      Hash: 802ea25fac59ef7a5dfb7eb06305cbf5
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: FixedRotationToUserConstraint
  - hash:
      serializedVersion: 2
      Hash: 540b4b9fa13abe2c96e1ae7c53b094d6
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: PlayerTriggerEvent
  - hash:
      serializedVersion: 2
      Hash: bad27d00e95650df455a1b4707d95161
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasBeveled
  - hash:
      serializedVersion: 2
      Hash: 1a60ce5e577a8ac2c5567d9c8c2fc7cc
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: FrontDocker
  - hash:
      serializedVersion: 2
      Hash: 634346ae4ed065e635a727d256173c3d
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction.NoneStirrupBeam
    className: Manager
  - hash:
      serializedVersion: 2
      Hash: 285f5d7c369d870b93f4a2cbcec118e4
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputDeviceDebuggerWindow
  - hash:
      serializedVersion: 2
      Hash: c5fd7bfc1806f78178f8b5f91e072387
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputSystemObject
  - hash:
      serializedVersion: 2
      Hash: 08705cef837f5d18a4c86b19b1ccf347
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: Vignette
  - hash:
      serializedVersion: 2
      Hash: 7d1a3e12579b7bd0f94909057ffba8d0
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: CameraSwitcher
  - hash:
      serializedVersion: 2
      Hash: 23b81d05a0ad0499613fc3e1a4a0501b
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalRenderPipelineEditorResources
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CameraEventRouter
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR
    className: MainMenu
  - hash:
      serializedVersion: 2
      Hash: 173ab614f790541721f56589bf7bab6a
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: HandConstraint
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: glTFast.dll
    namespaceName: GLTFast
    className: DontDestroyOnLoad
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ActivationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: fa33f14deec3f425d07e9b8cf9eeae52
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: BoundsControl
  - hash:
      serializedVersion: 2
      Hash: 05655f7deb066a961c41619ac9e98263
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ProBuilderMesh
  - hash:
      serializedVersion: 2
      Hash: ede3d522ce29a72deb2613abda57d11e
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: RemoteInputPlayerConnection
  - hash:
      serializedVersion: 2
      Hash: febe2d7bb0ecaa045be68e2635654b9c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: XRUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR
    className: VoiceManager
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UniTask.dll
    namespaceName: Cysharp.Threading.Tasks.Triggers
    className: AsyncDestroyTrigger
  - hash:
      serializedVersion: 2
      Hash: 24c74cc0d527a06113f2b967042769f0
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEditor.XR.LegacyInputHelpers
    className: CameraOffset
  - hash:
      serializedVersion: 2
      Hash: 647c40a15dcc1d8cc0f538f1a7260f81
    assemblyName: RhinoXDebugger.dll
    namespaceName: RhinoXDebugger
    className: DebuggerGlobalSetting
  - hash:
      serializedVersion: 2
      Hash: 5ab533713adae59372fb3e10a585c186
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AnimationPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: 4d51d5ccf37d2089bdfe827617b784ff
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: VolumeProfile
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: BaseInputOverride
  - hash:
      serializedVersion: 2
      Hash: e9389e944eb81a3a9df56fcdb09ce6a9
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: ColliderBehaviour
  - hash:
      serializedVersion: 2
      Hash: 9e6501692697e3b9d9f69d2caeaf6413
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: InteractablePulse
  - hash:
      serializedVersion: 2
      Hash: 77aaaaa51f7a8d8ff333a879741e47d2
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_InputField
  - hash:
      serializedVersion: 2
      Hash: 52bcb9c2a78bdd8d98a59e915c346e75
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: TransitionArmModel
  - hash:
      serializedVersion: 2
      Hash: 9c7c87712e298256d276eb96e02a57b1
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: PreferenceDictionary
  - hash:
      serializedVersion: 2
      Hash: 8bb99954ad60ced3debe38a0801108b2
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: HoverLight
  - hash:
      serializedVersion: 2
      Hash: c73b38d1734fde2c84da7bd282f19e54
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: Tonemapping
  - hash:
      serializedVersion: 2
      Hash: ba09c91fbd9fa0ddf95542d9f4f12c23
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: Light2D
  - hash:
      serializedVersion: 2
      Hash: 0e60655a65105e2189bae4c8749909ea
    assemblyName: Assembly-CSharp.dll
    namespaceName: LayerLab
    className: PanelControlSciFiSurvival
  - hash:
      serializedVersion: 2
      Hash: 7dbf450932234158be58ca2027563dcb
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector3
  - hash:
      serializedVersion: 2
      Hash: 53806940f8cb2a9a1e6d00641cef710f
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: Overlap
  - hash:
      serializedVersion: 2
      Hash: 300fd99066f8c288e85f5e81ce65fe34
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_FontAsset
  - hash:
      serializedVersion: 2
      Hash: 5e1eefcd5106de6c2e85908a3b2d3f04
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectToggle
  - hash:
      serializedVersion: 2
      Hash: 933b57d995257b082eb1fe22a1def0e7
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: FixedDistanceConstraint
  - hash:
      serializedVersion: 2
      Hash: 9f02af9ac352590ad9645f37d321299a
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: FloatingObject
  - hash:
      serializedVersion: 2
      Hash: 0c9920fb850aff7b5ec756815b7a6fc1
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ChannelMixer
  - hash:
      serializedVersion: 2
      Hash: b42b04b3084515287218921c80a4b542
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: Slider
  - hash:
      serializedVersion: 2
      Hash: 76eb44ba733054fa1ebc85ccc64177ea
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: MeshOutlineHierarchy
  - hash:
      serializedVersion: 2
      Hash: bbade81dac1a85d15cf55cfe40d08acd
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: BasicPressableButtonVisuals
  - hash:
      serializedVersion: 2
      Hash: 219b6caaad9662ed6a7d81ee3a9cd53d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedContinuousTurnProvider
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerUIntField
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFloatField
  - hash:
      serializedVersion: 2
      Hash: df819f0bc77c8d047eca1df91bbaa90d
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerPanel
  - hash:
      serializedVersion: 2
      Hash: c3d3430df9e3ea95e3e3d587961390fb
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: GroupTrack
  - hash:
      serializedVersion: 2
      Hash: 4e79aa027e6f47fb5dc45586c3ac9738
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: SimplePulsatingOutline
  - hash:
      serializedVersion: 2
      Hash: 54d8f12780f7f6df9b0603e66c9a5b3b
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ColorCurves
  - hash:
      serializedVersion: 2
      Hash: 3311df2c9196453241ceaa669a78f214
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ClippingPlane
  - hash:
      serializedVersion: 2
      Hash: e2549d3a59249ea600e260df1932d6f7
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: TrackedDevicePhysicsRaycaster
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit.Subsystems
    className: MRTKLifecycleManager
  - hash:
      serializedVersion: 2
      Hash: c5cd30fe1e9afca766d6bc6df1b4462b
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: ConstantViewSize
  - hash:
      serializedVersion: 2
      Hash: 553a35351c48212770ae3e63cda824cd
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRController
  - hash:
      serializedVersion: 2
      Hash: 63bdc2cf3273aa9bf650350f19d2af61
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: PixelPerfectCamera
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UniTask.dll
    namespaceName: Cysharp.Threading.Tasks.Triggers
    className: AsyncStartTrigger
  - hash:
      serializedVersion: 2
      Hash: a64b8ff703a69f3352c424e9d0bc1c49
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: VolumeComponent
  - hash:
      serializedVersion: 2
      Hash: 295831406da5907d41f58d84acd55d8d
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionEditorWindow
  - hash:
      serializedVersion: 2
      Hash: 5a842397fa20f89bd64fef644ff2a3fb
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: FontIconSet
  - hash:
      serializedVersion: 2
      Hash: 30f7754ece2a6847e69078802ab1d30f
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: RxAuthenticationEvent
  - hash:
      serializedVersion: 2
      Hash: 8416666a16d53b57bcfc8655265b9374
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: CanvasSliderVisuals
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: SceneLoader
  - hash:
      serializedVersion: 2
      Hash: fc169718e5fd24cea7425f24908c0418
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputManagerEditor
  - hash:
      serializedVersion: 2
      Hash: 06a2cc3809c32c33790a58f9ad54c342
    assemblyName: glTFast.dll
    namespaceName: GLTFast
    className: GltfAsset
  - hash:
      serializedVersion: 2
      Hash: 9c69646a0c902d3f02afaf7078787312
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: FaceUserConstraint
  - hash:
      serializedVersion: 2
      Hash: 6b3057497eb6f0e52612476af702bf8c
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: UIFoldout
  - hash:
      serializedVersion: 2
      Hash: 160e2bcbe40528676c78b632c197744d
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs
    className: InputActionManager
  - hash:
      serializedVersion: 2
      Hash: 05babe11b6b288bb89f3e4fcdb2cf590
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: RotationAxisConstraint
  - hash:
      serializedVersion: 2
      Hash: 394967a9e985e9f500a7abb65d4c08b5
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: ObjectBarAddRemoveItems
  - hash:
      serializedVersion: 2
      Hash: 7867e5ca948bd619ce5d648eb4261f96
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: RxControllerAdaptor
  - hash:
      serializedVersion: 2
      Hash: 663952b1721bab905a8e6d5b9e0b1caa
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: StateVisualizer
  - hash:
      serializedVersion: 2
      Hash: bd9d6ffcebcb6108ce6ddc422c554c40
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: Camera_Controller
  - hash:
      serializedVersion: 2
      Hash: d75eb815f0352618b330100ba8d7a989
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: Dialog
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Autodesk.Fbx.BuildTestAssets.dll
    namespaceName: Autodesk.Fbx.BuildTests
    className: ExportEmptyFbx
  - hash:
      serializedVersion: 2
      Hash: 38d2d948bfdb74d546343034c66af9a5
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: MRTKProfile
  - hash:
      serializedVersion: 2
      Hash: cf9f5cf2c3145a37ac07f00e50e460f9
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: AspectRatioFitter
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerValue
  - hash:
      serializedVersion: 2
      Hash: 760787567e1b60d64a3e421ab83ee14a
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextMeshProUGUI
  - hash:
      serializedVersion: 2
      Hash: 55aa63e24a9cdeefc3d0b05ddbe525fe
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: CameraSettingsManager
  - hash:
      serializedVersion: 2
      Hash: 1f0602e7bad1e0a9cba31ce292eee8bb
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerHBox
  - hash:
      serializedVersion: 2
      Hash: fedb0cbabb9c1c396860038b614fd697
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR
    className: PlayerController
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIntField
  - hash:
      serializedVersion: 2
      Hash: 76eb44ba733054fa1ebc85ccc64177ea
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: MeshOutline
  - hash:
      serializedVersion: 2
      Hash: 1f880cc188f7f5eb5592d4bf4f57fef5
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: CharacterControllerDriver
  - hash:
      serializedVersion: 2
      Hash: c1250ed3558f743c4c263e4ea9fc206a
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: ARCamera
  - hash:
      serializedVersion: 2
      Hash: 2bf2e9b2db51bf82e3ac18ad9e02e03f
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: DynamicTarget
  - hash:
      serializedVersion: 2
      Hash: 6b3393ea3e2ca084c8846f8a9413339d
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: GroundPlane
  - hash:
      serializedVersion: 2
      Hash: 6d8cd78f2c13b1681b472327912e1494
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: ObjectTrackingProfile
  - hash:
      serializedVersion: 2
      Hash: 38a94f5d58ee4d287b0099cc56f1366c
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: RhinoXGlobalSetting
  - hash:
      serializedVersion: 2
      Hash: 4868d7be96cf82e2d04a1a33a4309be7
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: RXButtonEventTrigger
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: RXEventSystem
  - hash:
      serializedVersion: 2
      Hash: 33d08c38e895181ca8e971b5fb10f5a3
    assemblyName: RhinoX-Unity.dll
    namespaceName: Ximmerse.RhinoX
    className: RXInputModule
  - hash:
      serializedVersion: 2
      Hash: 36112b0a16753cab0931521e4de67ef6
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: LiftGammaGain
  - hash:
      serializedVersion: 2
      Hash: acc03d769157cc9fca34b69272630623
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: Momentum
  - hash:
      serializedVersion: 2
      Hash: 2a0e287016c755ae9b3d7b449382854f
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX.Deprecated
    className: DialogShell
  - hash:
      serializedVersion: 2
      Hash: db9a22990098fac9878362236970a966
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: MRTKBaseInteractable
  - hash:
      serializedVersion: 2
      Hash: 5e8496257c481fb5c8806fcf2a0de11f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRSocketInteractor
  - hash:
      serializedVersion: 2
      Hash: bb6b9a25e695d7981ce2d853a697a828
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerVector4
  - hash:
      serializedVersion: 2
      Hash: 301f4da7cc3b301c161d089f9d22efed
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: Entity
  - hash:
      serializedVersion: 2
      Hash: 8d35f16c5022311c6f4d9432be73bfcf
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerIndirectFloatField
  - hash:
      serializedVersion: 2
      Hash: 6e7ae59d2ab83ab39b3ac8612abd98f8
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: TabView
  - hash:
      serializedVersion: 2
      Hash: b7d58df3c6d92c60e3611c0a808aa0dc
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerBitField
  - hash:
      serializedVersion: 2
      Hash: 2dd715d2fd9356d79575f3f85270aea4
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRRayInteractor
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: SimpleActivation
  - hash:
      serializedVersion: 2
      Hash: 7a7f9f5f58e21d02d71461ddf3f1e2b1
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: ScaleHandleInteractable
  - hash:
      serializedVersion: 2
      Hash: 6a5e9e09ca5b4c29d98a54fcc57d6fd1
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: AcrylicBlurFeature
  - hash:
      serializedVersion: 2
      Hash: 748dd2a2a28da4e618912da5ac45684b
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: StandaloneInputModule
  - hash:
      serializedVersion: 2
      Hash: 0cdbd6c7af464707503e684f63c6e844
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: ObjectOscilate
  - hash:
      serializedVersion: 2
      Hash: f7858108331b563fb41b80b729297acd
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Scrollbar
  - hash:
      serializedVersion: 2
      Hash: 5dea360fb0b147724ac344e6f399c2f3
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_SubMesh
  - hash:
      serializedVersion: 2
      Hash: c3d3430df9e3ea95e3e3d587961390fb
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: MarkerTrack
  - hash:
      serializedVersion: 2
      Hash: 8e6ceabfd38a184413d69b7db56c89cc
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools
    className: BeforeAfterTestCommandState
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: DebugUpdater
  - hash:
      serializedVersion: 2
      Hash: 89229a620f85ef21de336ab85a13d351
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerCanvas
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.EnhancedTouch
    className: TouchSimulation
  - hash:
      serializedVersion: 2
      Hash: 5aee9b3345ebb48c8e5925abe9821ed0
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasBackplate
  - hash:
      serializedVersion: 2
      Hash: 287a4aa7392bcfb309b4d7b4dc89eb3c
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: PlayerInputEditor
  - hash:
      serializedVersion: 2
      Hash: 5d5b0c61f3ab6ff5f41b1bbe19764526
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: XimmerseTrigger
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI.Editor
    className: InputSystemUIInputModuleEditor
  - hash:
      serializedVersion: 2
      Hash: 4fd8bf02e3089425d3502e26c44ba31a
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TextContainer
  - hash:
      serializedVersion: 2
      Hash: 166570adc156659892194734c9f81808
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: MaterialInstance
  - hash:
      serializedVersion: 2
      Hash: 0926f862783a6e002c21133220303929
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasQuadGlow
  - hash:
      serializedVersion: 2
      Hash: f79e983672d079ff24eee15484fb74b8
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerColor
  - hash:
      serializedVersion: 2
      Hash: c81cc58d58fd0183dacbdd22e96f9126
    assemblyName: Highlighters.dll
    namespaceName: Highlighters
    className: Highlighter
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.Editor
    className: InputActionAssetEditor
  - hash:
      serializedVersion: 2
      Hash: ee29c518c1506815647c105173f43312
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: DeviceBasedContinuousTurnProvider
  - hash:
      serializedVersion: 2
      Hash: e9389e944eb81a3a9df56fcdb09ce6a9
    assemblyName: Unity.ProBuilder.dll
    namespaceName: UnityEngine.ProBuilder
    className: TriggerBehaviour
  - hash:
      serializedVersion: 2
      Hash: 30418deb8b8ec715ae7b0d855cc01235
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: AcrylicLayerManager
  - hash:
      serializedVersion: 2
      Hash: d3f29ba27f28c1f558880c4df5545ed3
    assemblyName: Unity.InputSystem.dll
    namespaceName: 
    className: DownloadableSample
  - hash:
      serializedVersion: 2
      Hash: dbeab19c836bd50c0a0e371615445b64
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: FilmGrain
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: RXInteractionSystem
  - hash:
      serializedVersion: 2
      Hash: c2c69f9b9053f8291b360c082a9bc2ac
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRInteractorReticleVisual
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ScaleMeshEffect
  - hash:
      serializedVersion: 2
      Hash: caa533b001ae39b864ba8425019e2268
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Outline
  - hash:
      serializedVersion: 2
      Hash: cbac5ce6cd8008e3187fdd46b8716c14
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasElementMesh
  - hash:
      serializedVersion: 2
      Hash: 36025d42bf23fda83a55bf1429201821
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerRow
  - hash:
      serializedVersion: 2
      Hash: 76aef43e801605d1829c8805eeee135c
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputActionReference
  - hash:
      serializedVersion: 2
      Hash: 42ddce346702637ed4aa779ea7ddffac
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: Billboard
  - hash:
      serializedVersion: 2
      Hash: c3d3430df9e3ea95e3e3d587961390fb
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: PlayableTrack
  - hash:
      serializedVersion: 2
      Hash: 3164bc883ff663a5cdb32d1ef7e7c0c5
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: VerticalLayoutGroup
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRSingleGrabFreeTransformer
  - hash:
      serializedVersion: 2
      Hash: 5aea7b360f47fa710e993f05cfdace3e
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRControllerRecording
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UniTask.dll
    namespaceName: Cysharp.Threading.Tasks.Triggers
    className: AsyncAwakeTrigger
  - hash:
      serializedVersion: 2
      Hash: 23d143cd24ad1344a15a1254648db194
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasRadialSpinner
  - hash:
      serializedVersion: 2
      Hash: 18c222bef9ae745ce69ddde9b74337ca
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: Physics2DRaycaster
  - hash:
      serializedVersion: 2
      Hash: 77aaaaa51f7a8d8ff333a879741e47d2
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: MRTKTMPInputField
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: PlayModeRunnerCallback
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: PositionAsUV1
  - hash:
      serializedVersion: 2
      Hash: 6efea334e00a2e5c4497e46fb95b06dd
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRDirectInteractor
  - hash:
      serializedVersion: 2
      Hash: 87ff43d2baf3385cba327d8f803a4ebb
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: AudioPlayableAsset
  - hash:
      serializedVersion: 2
      Hash: b44598b10f19c3530827c81e14e36ce4
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: XRSimpleInteractable
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: UnityEngine.TestRunner.dll
    namespaceName: UnityEngine.TestTools.TestRunner.Callbacks
    className: RemoteTestResultSender
  - hash:
      serializedVersion: 2
      Hash: 134322c9293c1896357cce271b819ce4
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: EventTrigger
  - hash:
      serializedVersion: 2
      Hash: 3572e545eea274cc562042ab23a4529f
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: DeviceBasedSnapTurnProvider
  - hash:
      serializedVersion: 2
      Hash: 6943f561490fef37d732dae57a12c18d
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: InputSettings
  - hash:
      serializedVersion: 2
      Hash: 338dc8a38b6269f2235e151037257b3e
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: Orbital
  - hash:
      serializedVersion: 2
      Hash: a81d4c2c1cf3ad164d277d927a193679
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.UI
    className: Dropdown
  - hash:
      serializedVersion: 2
      Hash: 41a11e5e12848af5569b858de4102ec9
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit
    className: HandBounds
  - hash:
      serializedVersion: 2
      Hash: c3d3430df9e3ea95e3e3d587961390fb
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalTrack
  - hash:
      serializedVersion: 2
      Hash: 34eeaaab36e56c77ff26b47eb1d334c0
    assemblyName: glTFast.dll
    namespaceName: GLTFast
    className: TimeBudgetPerFrameDeferAgent
  - hash:
      serializedVersion: 2
      Hash: fbd69d745e943abfece5876821f74622
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: StatefulInteractableSpriteToggleVisuals
  - hash:
      serializedVersion: 2
      Hash: b5c6e8ebeb45db62242b1abb0f53b3f0
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: UGUIInputAdapterDraggable
  - hash:
      serializedVersion: 2
      Hash: 802ea25fac59ef7a5dfb7eb06305cbf5
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: MaintainApparentSizeConstraint
  - hash:
      serializedVersion: 2
      Hash: 48982140c520cbca517d0f96b60a0ba9
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: PostProcessData
  - hash:
      serializedVersion: 2
      Hash: f050e4ebd311386367bb1906f9218b01
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: UniversalAdditionalLightData
  - hash:
      serializedVersion: 2
      Hash: 578305bd925c0c4c8000e907d37ce5bc
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: ActionBasedContinuousMoveProvider
  - hash:
      serializedVersion: 2
      Hash: c3d3430df9e3ea95e3e3d587961390fb
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: ControlTrack
  - hash:
      serializedVersion: 2
      Hash: 75d82cdc8c4eb5f12570aa378266875e
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: InputSystemUIInputModule
  - hash:
      serializedVersion: 2
      Hash: 3c0fdbac4a2605c2f55fb61fe10b9a1c
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: RoundedRectMask2D
  - hash:
      serializedVersion: 2
      Hash: 48051ab090612e9dc53dbee57718c9b1
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorGraphicsToolsWireframe
  - hash:
      serializedVersion: 2
      Hash: 82d7e5a2e88c3a99fb49fb8fe876fb28
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: Follow
  - hash:
      serializedVersion: 2
      Hash: 0d63373dea0726b7fbd53117afe74acd
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction.NoneStirrupBeam
    className: FallRock
  - hash:
      serializedVersion: 2
      Hash: 686fefe323d8767e7263c284e3854f38
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: CanvasProxyInteractor
  - hash:
      serializedVersion: 2
      Hash: 134520f170644886bce038a4038a42c9
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: PaniniProjection
  - hash:
      serializedVersion: 2
      Hash: 893dbdbfe611d62e230a686ea89f1efd
    assemblyName: UnityEngine.XR.LegacyInputHelpers.dll
    namespaceName: UnityEngine.XR.LegacyInputHelpers
    className: SwingArmModel
  - hash:
      serializedVersion: 2
      Hash: 7aed3b69e71c6f8831dddd7b2962f21c
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Transformers
    className: XRDualGrabFreeTransformer
  - hash:
      serializedVersion: 2
      Hash: c1f5356fcd27668ddc25078e27f9828d
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.UI
    className: MultiplayerEventSystem
  - hash:
      serializedVersion: 2
      Hash: 72763ba72b43403ee09a4c54b07cb346
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem.XR
    className: TrackedPoseDriver
  - hash:
      serializedVersion: 2
      Hash: 577f93ac995e1db1cf5aec9ef4882a8f
    assemblyName: Microsoft.MixedReality.Toolkit.UXCore.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: RectTransformColliderFitter
  - hash:
      serializedVersion: 2
      Hash: 3311df2c9196453241ceaa669a78f214
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ClippingBox
  - hash:
      serializedVersion: 2
      Hash: 0a3fa1a367fddc65eb32515029503334
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerButton
  - hash:
      serializedVersion: 2
      Hash: 2f266363c59f60438ad265d79f7ca8d9
    assemblyName: Microsoft.MixedReality.Toolkit.SpatialManipulation.dll
    namespaceName: Microsoft.MixedReality.Toolkit.SpatialManipulation
    className: HandConstraintPalmUp
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Experimental.Rendering.Universal
    className: CinemachineUniversalPixelPerfect
  - hash:
      serializedVersion: 2
      Hash: 33b816dd93562ca6ce281ff195a7bfef
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation
    className: XRDeviceSimulator
  - hash:
      serializedVersion: 2
      Hash: 12abf21532d1e9e96cc3d6449d3ca03f
    assemblyName: Assembly-CSharp.dll
    namespaceName: Highlighters
    className: SimplePulsatingOverlay
  - hash:
      serializedVersion: 2
      Hash: c63ab2f7deb187da6261bd5e0df795e2
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction.NoneStirrupBeam
    className: Controller
  - hash:
      serializedVersion: 2
      Hash: 84e1b0a548d467bdaf3a16f89a2dbefd
    assemblyName: Unity.RenderPipelines.Universal.Runtime.dll
    namespaceName: UnityEngine.Rendering.Universal
    className: ForwardRendererData
  - hash:
      serializedVersion: 2
      Hash: 2c78508d67df37094377b29be4b72d85
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: FrontPlatePulse
  - hash:
      serializedVersion: 2
      Hash: 4d6cec8d5c37686e34f3059eac0f87ea
    assemblyName: RhinoX-Interaction-System.dll
    namespaceName: Ximmerse.RhinoX
    className: PlayerHandAnimator
  - hash:
      serializedVersion: 2
      Hash: e7a73f5f78ba6445195ec23b74a1ce2d
    assemblyName: Unity.TextMeshPro.dll
    namespaceName: TMPro
    className: TMP_ScrollbarEventHandler
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Microsoft.MixedReality.Toolkit.Core.dll
    namespaceName: Microsoft.MixedReality.Toolkit.Examples.Demos
    className: ConfigureSpatializationSettings
  - hash:
      serializedVersion: 2
      Hash: 8c328a0ffa03c1bcb9332bb39f4f8453
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering
    className: Volume
  - hash:
      serializedVersion: 2
      Hash: cef7651ddeb949eae74c2a92489f07e5
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit.UI
    className: TrackedDeviceGraphicRaycaster
  - hash:
      serializedVersion: 2
      Hash: fa52428ef3e6aed096308a1a40a74e70
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: ProximityLight
  - hash:
      serializedVersion: 2
      Hash: a79e49c7d950fed24861d564bd86141b
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasGlow
  - hash:
      serializedVersion: 2
      Hash: 7931294ef54f62d0ee71c0696777ce27
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasElementBeveledRect
  - hash:
      serializedVersion: 2
      Hash: 36025d42bf23fda83a55bf1429201821
    assemblyName: Unity.RenderPipelines.Core.Runtime.dll
    namespaceName: UnityEngine.Rendering.UI
    className: DebugUIHandlerFoldout
  - hash:
      serializedVersion: 2
      Hash: 71bb6a6b6c8f052f948db64c7dd3ca4f
    assemblyName: Assembly-CSharp.dll
    namespaceName: 
    className: Test
  - hash:
      serializedVersion: 2
      Hash: 2f5dc24416436e8ab1e97e45d7beccc9
    assemblyName: Microsoft.MixedReality.Toolkit.UXComponents.NonCanvas.dll
    namespaceName: Microsoft.MixedReality.Toolkit.UX
    className: UXThemeProfile
  - hash:
      serializedVersion: 2
      Hash: 9bcfa06fefdab59020677dcce3f06fad
    assemblyName: Unity.XR.Interaction.Toolkit.dll
    namespaceName: UnityEngine.XR.Interaction.Toolkit
    className: GrabMoveProvider
  - hash:
      serializedVersion: 2
      Hash: 8220b46879dc5e37db1b940c938f5744
    assemblyName: Unity.Timeline.dll
    namespaceName: UnityEngine.Timeline
    className: SignalEmitter
  - hash:
      serializedVersion: 2
      Hash: 766d1e816bcaf02d44dc1df06af2bf9a
    assemblyName: Unity.InputSystem.dll
    namespaceName: UnityEngine.InputSystem
    className: PlayerInputManager
  - hash:
      serializedVersion: 2
      Hash: f80a1aef73ca1487a7fe58d5280474dc
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasFrontplate
  - hash:
      serializedVersion: 2
      Hash: 2a45e7f11b4d0602108f5d828c998c96
    assemblyName: Microsoft.MixedReality.GraphicsTools.dll
    namespaceName: Microsoft.MixedReality.GraphicsTools
    className: CanvasMaterialAnimatorCanvasProgressBar
  - hash:
      serializedVersion: 2
      Hash: abf1930d8126d0b2e00e9449129a0190
    assemblyName: UnityEngine.UI.dll
    namespaceName: UnityEngine.EventSystems
    className: TouchInputModule
  - hash:
      serializedVersion: 2
      Hash: ec92925cb9ca6fb0e165409d2cdaac93
    assemblyName: Assembly-CSharp.dll
    namespaceName: IndustryMR.ComponentDestruction
    className: EditorHelper
  - hash:
      serializedVersion: 2
      Hash: 704dcd31abc789439bc3f3cd10b58c2b
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenComponent
  - hash:
      serializedVersion: 2
      Hash: 23206d8cacf2b6e30fd52e948d6c73f5
    assemblyName: DOTween.dll
    namespaceName: DG.Tweening.Core
    className: DOTweenSettings
  platform: 19
  scenePathNames:
  - Assets/Start.unity
  - Assets/Scenes/MainMenu.unity
  - Assets/_Project/ComponentDestruction/Scenes/ComponentDestruction_MainMenu.unity
  - Assets/_Project/ComponentDestruction/Scenes/NoneStirrupBeam.unity
  - Assets/_Project/_01_HighAlt/Scenes/_HighAlt_ObjectDrop.unity
  playerPath: F:/LongProjects/IndustryMR/Builds/Windows/IndustryMR.exe
