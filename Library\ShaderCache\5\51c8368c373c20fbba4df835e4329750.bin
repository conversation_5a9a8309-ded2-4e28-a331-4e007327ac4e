  <Q                           O  #ifdef VERTEX
#version 300 es

in highp vec4 in_POSITION0;
in highp vec2 in_TEXCOORD0;
out highp vec2 vs_TEXCOORD0;
void main()
{
    gl_Position.xyz = in_POSITION0.xyz;
    gl_Position.w = 1.0;
    vs_TEXCOORD0.xy = in_TEXCOORD0.xy + vec2(9.99999997e-07, 9.99999997e-07);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SSAOParams;
uniform 	vec4 _SourceSize;
UNITY_LOCATION(0) uniform mediump sampler2D _BaseMap;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
float u_xlat1;
mediump vec4 u_xlat16_1;
vec4 u_xlat2;
mediump vec4 u_xlat16_2;
mediump vec4 u_xlat16_3;
mediump vec4 u_xlat16_4;
vec3 u_xlat5;
vec3 u_xlat6;
vec3 u_xlat7;
vec3 u_xlat9;
float u_xlat10;
float u_xlat15;
void main()
{
    u_xlat0.x = float(1.0) / float(_SSAOParams.z);
    u_xlat5.xy = (-_SourceSize.zw) * u_xlat0.xx + vs_TEXCOORD0.xy;
    u_xlat16_1 = texture(_BaseMap, u_xlat5.xy);
    u_xlat5.xyz = u_xlat16_1.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat16_2 = texture(_BaseMap, vs_TEXCOORD0.xy);
    u_xlat6.xyz = u_xlat16_2.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat5.x = dot(u_xlat6.xyz, u_xlat5.xyz);
    u_xlat5.x = u_xlat5.x + -0.800000012;
    u_xlat5.x = u_xlat5.x * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat5.x = min(max(u_xlat5.x, 0.0), 1.0);
#else
    u_xlat5.x = clamp(u_xlat5.x, 0.0, 1.0);
#endif
    u_xlat10 = u_xlat5.x * -2.0 + 3.0;
    u_xlat5.x = u_xlat5.x * u_xlat5.x;
    u_xlat5.x = u_xlat5.x * u_xlat10;
    u_xlat10 = u_xlat16_1.x * u_xlat5.x + u_xlat16_2.x;
    u_xlat2.xy = u_xlat0.xx * _SourceSize.zw;
    u_xlat0.xw = _SourceSize.zw * u_xlat0.xx + vs_TEXCOORD0.xy;
    u_xlat16_3 = texture(_BaseMap, u_xlat0.xw);
    u_xlat2.zw = (-u_xlat2.yx);
    u_xlat2 = u_xlat2.xzwy + vs_TEXCOORD0.xyxy;
    u_xlat16_4 = texture(_BaseMap, u_xlat2.xy);
    u_xlat16_2 = texture(_BaseMap, u_xlat2.zw);
    u_xlat9.xyz = u_xlat16_4.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat0.x = dot(u_xlat6.xyz, u_xlat9.xyz);
    u_xlat0.x = u_xlat0.x + -0.800000012;
    u_xlat0.x = u_xlat0.x * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat0.x = min(max(u_xlat0.x, 0.0), 1.0);
#else
    u_xlat0.x = clamp(u_xlat0.x, 0.0, 1.0);
#endif
    u_xlat15 = u_xlat0.x * -2.0 + 3.0;
    u_xlat0.x = u_xlat0.x * u_xlat0.x;
    u_xlat1 = u_xlat0.x * u_xlat15;
    u_xlat0.x = u_xlat15 * u_xlat0.x + u_xlat5.x;
    u_xlat5.x = u_xlat16_4.x * u_xlat1 + u_xlat10;
    u_xlat7.xyz = u_xlat16_2.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat10 = dot(u_xlat6.xyz, u_xlat7.xyz);
    u_xlat10 = u_xlat10 + -0.800000012;
    u_xlat10 = u_xlat10 * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat10 = min(max(u_xlat10, 0.0), 1.0);
#else
    u_xlat10 = clamp(u_xlat10, 0.0, 1.0);
#endif
    u_xlat15 = u_xlat10 * -2.0 + 3.0;
    u_xlat10 = u_xlat10 * u_xlat10;
    u_xlat1 = u_xlat10 * u_xlat15;
    u_xlat0.x = u_xlat15 * u_xlat10 + u_xlat0.x;
    u_xlat5.x = u_xlat16_2.x * u_xlat1 + u_xlat5.x;
    u_xlat2.xyz = u_xlat16_3.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat10 = dot(u_xlat6.xyz, u_xlat2.xyz);
    u_xlat10 = u_xlat10 + -0.800000012;
    u_xlat10 = u_xlat10 * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat10 = min(max(u_xlat10, 0.0), 1.0);
#else
    u_xlat10 = clamp(u_xlat10, 0.0, 1.0);
#endif
    u_xlat15 = u_xlat10 * -2.0 + 3.0;
    u_xlat10 = u_xlat10 * u_xlat10;
    u_xlat1 = u_xlat10 * u_xlat15;
    u_xlat0.x = u_xlat15 * u_xlat10 + u_xlat0.x;
    u_xlat0.x = u_xlat0.x + 1.0;
    u_xlat0.x = float(1.0) / float(u_xlat0.x);
    u_xlat5.x = u_xlat16_3.x * u_xlat1 + u_xlat5.x;
    u_xlat0 = (-u_xlat5.xxxx) * u_xlat0.xxxx + vec4(1.0, 1.0, 1.0, 1.0);
    SV_Target0 = u_xlat0;
    return;
}

#endif
                              $Globals          _SSAOParams                          _SourceSize                                _BaseMap                   