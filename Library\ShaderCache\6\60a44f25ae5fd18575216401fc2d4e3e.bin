4  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL   	   _BLOOM_HQ      _CHROMATIC_ABERRATION      _LINEAR_TO_SRGB_CONVERSION     _TONEMAP_NEUTRALl0  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if H<PERSON><PERSON>C_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	vec4 _Bloom_Params;
uniform 	float _Bloom_RGBM;
uniform 	float _Chroma_Params;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
uniform 	vec4 _Bloom_Texture_TexelSize;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2DArray _Bloom_Texture;
UNITY_LOCATION(2) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(3) uniform mediump sampler2D _UserLut;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec4 u_xlat10_0;
uint u_xlatu0;
bvec3 u_xlatb0;
vec4 u_xlat1;
mediump vec3 u_xlat16_1;
bool u_xlatb1;
vec3 u_xlat2;
mediump vec3 u_xlat16_2;
mediump vec3 u_xlat10_2;
vec4 u_xlat3;
mediump vec4 u_xlat16_3;
mediump vec4 u_xlat10_3;
vec4 u_xlat4;
mediump vec2 u_xlat16_4;
mediump vec4 u_xlat10_4;
vec3 u_xlat5;
mediump vec3 u_xlat10_5;
vec2 u_xlat6;
mediump vec4 u_xlat10_6;
mediump vec3 u_xlat16_7;
mediump vec3 u_xlat16_8;
mediump vec3 u_xlat16_9;
float u_xlat10;
mediump vec3 u_xlat10_10;
vec2 u_xlat21;
vec2 u_xlat25;
float u_xlat30;
bool u_xlatb30;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat1 = vs_TEXCOORD0.xyxy * vec4(2.0, 2.0, 2.0, 2.0) + vec4(-1.0, -1.0, -1.0, -1.0);
    u_xlat10 = dot(u_xlat1.zw, u_xlat1.zw);
    u_xlat1 = vec4(u_xlat10) * u_xlat1;
    u_xlat1 = u_xlat1 * vec4(_Chroma_Params);
    u_xlat0.z = float(u_xlatu0);
    u_xlat0.xy = vs_TEXCOORD0.xy;
    u_xlat10_2.x = texture(_SourceTex, u_xlat0.xyz).x;
    u_xlat1 = u_xlat1 * vec4(-0.333333343, -0.333333343, -0.666666687, -0.666666687) + vs_TEXCOORD0.xyxy;
    u_xlat0.xy = u_xlat1.xy;
    u_xlat10_2.y = texture(_SourceTex, u_xlat0.xyz).y;
    u_xlat0.xy = u_xlat1.zw;
    u_xlat10_2.z = texture(_SourceTex, u_xlat0.xyz).z;
    u_xlat1.xy = vs_TEXCOORD0.xy * _Bloom_Texture_TexelSize.zw + vec2(0.5, 0.5);
    u_xlat21.xy = floor(u_xlat1.xy);
    u_xlat1.xy = fract(u_xlat1.xy);
    u_xlat16_3 = (-u_xlat1.xyxy) * vec4(0.5, 0.5, 0.166666672, 0.166666672) + vec4(0.5, 0.5, 0.5, 0.5);
    u_xlat16_3 = u_xlat1.xyxy * u_xlat16_3 + vec4(0.5, 0.5, -0.5, -0.5);
    u_xlat16_4.xy = u_xlat1.xy * vec2(0.5, 0.5) + vec2(-1.0, -1.0);
    u_xlat16_4.xy = u_xlat1.xy * u_xlat16_4.xy;
    u_xlat16_4.xy = u_xlat16_4.xy * u_xlat1.xy + vec2(0.666666687, 0.666666687);
    u_xlat16_3 = u_xlat1.xyxy * u_xlat16_3 + vec4(0.166666672, 0.166666672, 0.166666672, 0.166666672);
    u_xlat1.xy = (-u_xlat16_4.xy) + vec2(1.0, 1.0);
    u_xlat1.xy = (-u_xlat16_3.xy) + u_xlat1.xy;
    u_xlat1.xy = (-u_xlat16_3.zw) + u_xlat1.xy;
    u_xlat5.xy = u_xlat16_4.xy + u_xlat16_3.zw;
    u_xlat25.xy = u_xlat1.xy + u_xlat16_3.xy;
    u_xlat6.xy = vec2(1.0) / vec2(u_xlat5.xy);
    u_xlat3.zw = u_xlat16_4.xy * u_xlat6.xy + vec2(-1.0, -1.0);
    u_xlat6.xy = vec2(1.0) / vec2(u_xlat25.xy);
    u_xlat3.xy = u_xlat1.xy * u_xlat6.xy + vec2(1.0, 1.0);
    u_xlat4 = u_xlat21.xyxy + u_xlat3.zwxw;
    u_xlat4 = u_xlat4 + vec4(-0.5, -0.5, -0.5, -0.5);
    u_xlat4 = u_xlat4 * _Bloom_Texture_TexelSize.xyxy;
    u_xlat0.xy = min(u_xlat4.xy, vec2(1.0, 1.0));
    u_xlat10_6 = textureLod(_Bloom_Texture, u_xlat0.xyz, 0.0);
    u_xlat0.xy = min(u_xlat4.zw, vec2(1.0, 1.0));
    u_xlat10_4 = textureLod(_Bloom_Texture, u_xlat0.xyz, 0.0);
    u_xlat4 = u_xlat10_4 * u_xlat25.xxxx;
    u_xlat4 = u_xlat5.xxxx * u_xlat10_6 + u_xlat4;
    u_xlat1 = u_xlat21.xyxy + u_xlat3.zyxy;
    u_xlat1 = u_xlat1 + vec4(-0.5, -0.5, -0.5, -0.5);
    u_xlat1 = u_xlat1 * _Bloom_Texture_TexelSize.xyxy;
    u_xlat0.xy = min(u_xlat1.xy, vec2(1.0, 1.0));
    u_xlat10_3 = textureLod(_Bloom_Texture, u_xlat0.xyz, 0.0);
    u_xlat0.xy = min(u_xlat1.zw, vec2(1.0, 1.0));
    u_xlat10_0 = textureLod(_Bloom_Texture, u_xlat0.xyz, 0.0);
    u_xlat0 = u_xlat10_0 * u_xlat25.xxxx;
    u_xlat0 = u_xlat5.xxxx * u_xlat10_3 + u_xlat0;
    u_xlat0 = u_xlat0 * u_xlat25.yyyy;
    u_xlat0 = u_xlat5.yyyy * u_xlat4 + u_xlat0;
#ifdef UNITY_ADRENO_ES3
    u_xlatb1 = !!(0.0<_Bloom_RGBM);
#else
    u_xlatb1 = 0.0<_Bloom_RGBM;
#endif
    if(u_xlatb1){
        u_xlat16_7.xyz = u_xlat0.www * u_xlat0.xyz;
        u_xlat1.xyz = u_xlat16_7.xyz * vec3(8.0, 8.0, 8.0);
        u_xlat16_1.xyz = u_xlat1.xyz;
    } else {
        u_xlat16_1.xyz = u_xlat0.xyz;
    }
    u_xlat0.xyz = u_xlat16_1.xyz * _Bloom_Params.xxx;
    u_xlat0.xyz = u_xlat0.xyz * _Bloom_Params.yzw + u_xlat10_2.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlatb30 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb30 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb30){
        u_xlat2.xy = vs_TEXCOORD0.xy + (-_Vignette_Params2.xy);
        u_xlat2.yz = abs(u_xlat2.xy) * _Vignette_Params2.zz;
        u_xlat2.x = u_xlat2.y * _Vignette_Params1.w;
        u_xlat30 = dot(u_xlat2.xz, u_xlat2.xz);
        u_xlat30 = (-u_xlat30) + 1.0;
        u_xlat30 = max(u_xlat30, 0.0);
        u_xlat30 = log2(u_xlat30);
        u_xlat30 = u_xlat30 * _Vignette_Params2.w;
        u_xlat30 = exp2(u_xlat30);
        u_xlat2.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat2.xyz = vec3(u_xlat30) * u_xlat2.xyz + _Vignette_Params1.xyz;
        u_xlat2.xyz = u_xlat0.xyz * u_xlat2.xyz;
        u_xlat16_2.xyz = u_xlat2.xyz;
    } else {
        u_xlat16_2.xyz = u_xlat0.xyz;
    }
    u_xlat0.xyz = u_xlat16_2.xyz * _Lut_Params.www;
    u_xlat16_7.xyz = min(u_xlat0.xyz, vec3(435.187134, 435.187134, 435.187134));
    u_xlat16_8.xyz = u_xlat16_7.xyz * vec3(1.31338608, 1.31338608, 1.31338608);
    u_xlat16_9.xyz = u_xlat16_7.xyz * vec3(0.262677222, 0.262677222, 0.262677222) + vec3(0.0695999935, 0.0695999935, 0.0695999935);
    u_xlat16_9.xyz = u_xlat16_8.xyz * u_xlat16_9.xyz + vec3(0.00543999998, 0.00543999998, 0.00543999998);
    u_xlat16_7.xyz = u_xlat16_7.xyz * vec3(0.262677222, 0.262677222, 0.262677222) + vec3(0.289999992, 0.289999992, 0.289999992);
    u_xlat16_7.xyz = u_xlat16_8.xyz * u_xlat16_7.xyz + vec3(0.0816000104, 0.0816000104, 0.0816000104);
    u_xlat16_7.xyz = u_xlat16_9.xyz / u_xlat16_7.xyz;
    u_xlat16_7.xyz = u_xlat16_7.xyz + vec3(-0.0666666627, -0.0666666627, -0.0666666627);
    u_xlat16_7.xyz = u_xlat16_7.xyz * vec3(1.31338608, 1.31338608, 1.31338608);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_7.xyz = min(max(u_xlat16_7.xyz, 0.0), 1.0);
#else
    u_xlat16_7.xyz = clamp(u_xlat16_7.xyz, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb0.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb0.x){
        u_xlat16_8.xyz = u_xlat16_7.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_9.xyz = log2(u_xlat16_7.xyz);
        u_xlat16_9.xyz = u_xlat16_9.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_9.xyz = exp2(u_xlat16_9.xyz);
        u_xlat16_9.xyz = u_xlat16_9.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_7.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_8;
            hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_8.x : u_xlat16_9.x;
            hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_8.y : u_xlat16_9.y;
            hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_8.z : u_xlat16_9.z;
            u_xlat16_8 = hlslcc_movcTemp;
        }
        u_xlat0.xyz = u_xlat16_8.zxy * _UserLut_Params.zzz;
        u_xlat0.x = floor(u_xlat0.x);
        u_xlat5.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat5.yz = u_xlat0.yz * _UserLut_Params.xy + u_xlat5.xy;
        u_xlat5.x = u_xlat0.x * _UserLut_Params.y + u_xlat5.y;
        u_xlat10_10.xyz = textureLod(_UserLut, u_xlat5.xz, 0.0).xyz;
        u_xlat6.x = _UserLut_Params.y;
        u_xlat6.y = 0.0;
        u_xlat5.xy = u_xlat5.xz + u_xlat6.xy;
        u_xlat10_5.xyz = textureLod(_UserLut, u_xlat5.xy, 0.0).xyz;
        u_xlat0.x = u_xlat16_8.z * _UserLut_Params.z + (-u_xlat0.x);
        u_xlat5.xyz = (-u_xlat10_10.xyz) + u_xlat10_5.xyz;
        u_xlat0.xyz = u_xlat0.xxx * u_xlat5.xyz + u_xlat10_10.xyz;
        u_xlat0.xyz = (-u_xlat16_8.xyz) + u_xlat0.xyz;
        u_xlat0.xyz = _UserLut_Params.www * u_xlat0.xyz + u_xlat16_8.xyz;
        u_xlat16_8.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_9.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_9.xyz = u_xlat16_9.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_9.xyz = log2(abs(u_xlat16_9.xyz));
        u_xlat16_9.xyz = u_xlat16_9.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_9.xyz = exp2(u_xlat16_9.xyz);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
        u_xlat16_7.x = (u_xlatb0.x) ? u_xlat16_8.x : u_xlat16_9.x;
        u_xlat16_7.y = (u_xlatb0.y) ? u_xlat16_8.y : u_xlat16_9.y;
        u_xlat16_7.z = (u_xlatb0.z) ? u_xlat16_8.z : u_xlat16_9.z;
    }
    u_xlat0.xyz = u_xlat16_7.zxy * _Lut_Params.zzz;
    u_xlat0.x = floor(u_xlat0.x);
    u_xlat5.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat5.yz = u_xlat0.yz * _Lut_Params.xy + u_xlat5.xy;
    u_xlat5.x = u_xlat0.x * _Lut_Params.y + u_xlat5.y;
    u_xlat10_10.xyz = textureLod(_InternalLut, u_xlat5.xz, 0.0).xyz;
    u_xlat6.x = _Lut_Params.y;
    u_xlat6.y = 0.0;
    u_xlat5.xy = u_xlat5.xz + u_xlat6.xy;
    u_xlat10_5.xyz = textureLod(_InternalLut, u_xlat5.xy, 0.0).xyz;
    u_xlat0.x = u_xlat16_7.z * _Lut_Params.z + (-u_xlat0.x);
    u_xlat5.xyz = (-u_xlat10_10.xyz) + u_xlat10_5.xyz;
    u_xlat0.xyz = u_xlat0.xxx * u_xlat5.xyz + u_xlat10_10.xyz;
    u_xlat16_7.xyz = u_xlat0.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlat16_8.xyz = log2(abs(u_xlat0.xyz));
    u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_8.xyz = exp2(u_xlat16_8.xyz);
    u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat0.xyzx).xyz;
    SV_Target0.x = (u_xlatb0.x) ? u_xlat16_7.x : u_xlat16_8.x;
    SV_Target0.y = (u_xlatb0.y) ? u_xlat16_7.y : u_xlat16_8.y;
    SV_Target0.z = (u_xlatb0.z) ? u_xlat16_7.z : u_xlat16_8.z;
    SV_Target0.w = 1.0;
    return;
}

#endif
                              $Globalsp         _Lut_Params                          _UserLut_Params                      
   _Bloom_Params                            _Bloom_RGBM                   0      _Chroma_Params                    4      _Vignette_Params1                     @      _Vignette_Params2                     P      _Bloom_Texture_TexelSize                  `          $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      _Bloom_Texture            
      _InternalLut                _UserLut                UnityStereoEyeIndices                  