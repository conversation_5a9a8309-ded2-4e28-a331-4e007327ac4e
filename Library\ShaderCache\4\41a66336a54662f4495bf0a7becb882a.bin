\Y  <Q                         STEREO_MULTIVIEW_ON       _SMAA_PRESET_HIGH   TW  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 hlslcc_mtx4x4_FullscreenProjMat[4];
uniform 	vec4 _Metrics;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
in highp vec4 in_POSITION0;
in highp vec2 in_TEXCOORD0;
out highp vec2 vs_TEXCOORD0;
out highp vec2 vs_TEXCOORD1;
out highp vec4 vs_TEXCOORD2;
out highp vec4 vs_TEXCOORD3;
out highp vec4 vs_TEXCOORD4;
out highp float vs_BLENDWEIGHT0;
vec4 u_xlat0;
uint u_xlatu0;
vec4 u_xlat1;
void main()
{
    u_xlat0 = in_POSITION0.yyyy * hlslcc_mtx4x4_FullscreenProjMat[1];
    u_xlat0 = hlslcc_mtx4x4_FullscreenProjMat[0] * in_POSITION0.xxxx + u_xlat0;
    u_xlat0 = hlslcc_mtx4x4_FullscreenProjMat[2] * in_POSITION0.zzzz + u_xlat0;
    gl_Position = u_xlat0 + hlslcc_mtx4x4_FullscreenProjMat[3];
    vs_TEXCOORD1.xy = in_TEXCOORD0.xy * _Metrics.zw;
    vs_TEXCOORD0.xy = in_TEXCOORD0.xy;
    u_xlat0 = _Metrics.xxyy * vec4(-0.25, 1.25, -0.125, -0.125) + in_TEXCOORD0.xxyy;
    vs_TEXCOORD2 = u_xlat0.xzyw;
    u_xlat1 = _Metrics.xyxy * vec4(-0.125, -0.25, -0.125, 1.25) + in_TEXCOORD0.xyxy;
    vs_TEXCOORD3 = u_xlat1;
    u_xlat0.zw = u_xlat1.yw;
    vs_TEXCOORD4 = _Metrics.xxyy * vec4(-32.0, 32.0, -32.0, 32.0) + u_xlat0;
    u_xlatu0 = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Metrics;
UNITY_LOCATION(0) uniform mediump sampler2DArray _ColorTexture;
UNITY_LOCATION(1) uniform mediump sampler2D _AreaTexture;
UNITY_LOCATION(2) uniform mediump sampler2D _SearchTexture;
in highp vec2 vs_TEXCOORD0;
in highp vec2 vs_TEXCOORD1;
in highp vec4 vs_TEXCOORD2;
in highp vec4 vs_TEXCOORD3;
in highp vec4 vs_TEXCOORD4;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out highp vec4 SV_Target0;
vec4 u_xlat0;
mediump float u_xlat10_0;
uint u_xlatu0;
vec3 u_xlat1;
mediump vec2 u_xlat10_1;
bool u_xlatb1;
vec4 u_xlat2;
mediump vec3 u_xlat10_2;
vec4 u_xlat3;
mediump vec3 u_xlat10_3;
bvec4 u_xlatb3;
vec4 u_xlat4;
mediump vec3 u_xlat10_4;
bool u_xlatb4;
vec4 u_xlat5;
bvec4 u_xlatb5;
vec4 u_xlat6;
mediump vec3 u_xlat10_6;
vec3 u_xlat7;
mediump vec4 u_xlat10_8;
mediump float u_xlat10_9;
vec3 u_xlat10;
mediump vec3 u_xlat10_10;
bool u_xlatb10;
vec3 u_xlat11;
mediump vec3 u_xlat10_11;
bvec3 u_xlatb11;
vec3 u_xlat12;
vec3 u_xlat13;
vec2 u_xlat19;
mediump float u_xlat10_19;
bool u_xlatb19;
vec2 u_xlat20;
mediump float u_xlat10_20;
bool u_xlatb20;
vec2 u_xlat21;
mediump vec2 u_xlat10_21;
bool u_xlatb21;
float u_xlat28;
mediump float u_xlat10_28;
bool u_xlatb28;
float u_xlat29;
mediump float u_xlat10_29;
bool u_xlatb29;
mediump float u_xlat10_30;
bool u_xlatb30;
float u_xlat31;
bool u_xlatb32;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat0.w = float(u_xlatu0);
    u_xlat0.xy = vs_TEXCOORD0.xy;
    u_xlat1.xy = texture(_ColorTexture, u_xlat0.xyw).xy;
#ifdef UNITY_ADRENO_ES3
    u_xlatb10 = !!(0.0<u_xlat1.y);
#else
    u_xlatb10 = 0.0<u_xlat1.y;
#endif
    if(u_xlatb10){
#ifdef UNITY_ADRENO_ES3
        u_xlatb10 = !!(0.0<u_xlat1.x);
#else
        u_xlatb10 = 0.0<u_xlat1.x;
#endif
        if(u_xlatb10){
            u_xlat2.xy = _Metrics.xy * vec2(-1.0, 1.0);
            u_xlat2.z = 1.0;
            u_xlat0.xy = vs_TEXCOORD0.xy;
            u_xlat10_10.x = 0.0;
            u_xlat0.z = -1.0;
            u_xlat3.x = 1.0;
            while(true){
#ifdef UNITY_ADRENO_ES3
                u_xlatb28 = !!(u_xlat0.z<7.0);
#else
                u_xlatb28 = u_xlat0.z<7.0;
#endif
#ifdef UNITY_ADRENO_ES3
                u_xlatb29 = !!(0.899999976<u_xlat3.x);
#else
                u_xlatb29 = 0.899999976<u_xlat3.x;
#endif
                u_xlatb28 = u_xlatb28 && u_xlatb29;
                if(!u_xlatb28){break;}
                u_xlat0.xyz = u_xlat2.xyz + u_xlat0.xyz;
                u_xlat10_10.xy = textureLod(_ColorTexture, u_xlat0.xyw, 0.0).yx;
                u_xlat3.x = dot(u_xlat10_10.yx, vec2(0.5, 0.5));
            }
#ifdef UNITY_ADRENO_ES3
            u_xlatb10 = !!(0.899999976<u_xlat10_10.x);
#else
            u_xlatb10 = 0.899999976<u_xlat10_10.x;
#endif
            u_xlat10.x = u_xlatb10 ? 1.0 : float(0.0);
            u_xlat2.x = u_xlat0.z + u_xlat10.x;
        } else {
            u_xlat2.x = 0.0;
            u_xlat3.x = 0.0;
        }
        u_xlat10.xy = _Metrics.xy * vec2(1.0, -1.0);
        u_xlat10.z = 1.0;
        u_xlat4.yz = vs_TEXCOORD0.xy;
        u_xlat4.x = float(-1.0);
        u_xlat31 = float(1.0);
        while(true){
#ifdef UNITY_ADRENO_ES3
            u_xlatb21 = !!(u_xlat4.x<7.0);
#else
            u_xlatb21 = u_xlat4.x<7.0;
#endif
#ifdef UNITY_ADRENO_ES3
            u_xlatb30 = !!(0.899999976<u_xlat31);
#else
            u_xlatb30 = 0.899999976<u_xlat31;
#endif
            u_xlatb21 = u_xlatb30 && u_xlatb21;
            if(!u_xlatb21){break;}
            u_xlat0.xyz = u_xlat10.xyz + u_xlat4.yzx;
            u_xlat10_21.xy = textureLod(_ColorTexture, u_xlat0.xyw, 0.0).xy;
            u_xlat31 = dot(u_xlat10_21.xy, vec2(0.5, 0.5));
            u_xlat4.xyz = u_xlat0.zxy;
        }
        u_xlat3.y = u_xlat31;
        u_xlat10.x = u_xlat2.x + u_xlat4.x;
#ifdef UNITY_ADRENO_ES3
        u_xlatb10 = !!(2.0<u_xlat10.x);
#else
        u_xlatb10 = 2.0<u_xlat10.x;
#endif
        if(u_xlatb10){
            u_xlat2.y = (-u_xlat2.x) + 0.25;
            u_xlat2.zw = u_xlat4.xx * vec2(1.0, -1.0) + vec2(0.0, -0.25);
            u_xlat4 = u_xlat2.yxzw * _Metrics.xyxy + vs_TEXCOORD0.xyxy;
            u_xlat4 = _Metrics.xyxy * vec4(-1.0, 0.0, 1.0, 0.0) + u_xlat4;
            u_xlat5.xy = u_xlat4.xy;
            u_xlat5.z = u_xlat0.w;
            u_xlat10_10.xy = textureLod(_ColorTexture, u_xlat5.xyz, 0.0).xy;
            u_xlat5.xy = u_xlat4.zw;
            u_xlat10_11.xz = textureLod(_ColorTexture, u_xlat5.xyz, 0.0).xy;
            u_xlat10_10.z = u_xlat10_11.x;
            u_xlat21.xy = u_xlat10_10.xz * vec2(5.0, 5.0) + vec2(-3.75, -3.75);
            u_xlat10.xz = u_xlat10_10.xz * abs(u_xlat21.xy);
            u_xlat10.xz = roundEven(u_xlat10.xz);
            u_xlat13.x = roundEven(u_xlat10_10.y);
            u_xlat13.z = roundEven(u_xlat10_11.z);
            u_xlat10.xy = u_xlat13.xz * vec2(2.0, 2.0) + u_xlat10.xz;
            u_xlatb11.xz = greaterThanEqual(u_xlat3.xxyy, vec4(0.899999976, 0.0, 0.899999976, 0.899999976)).xz;
            {
                vec3 hlslcc_movcTemp = u_xlat10;
                hlslcc_movcTemp.x = (u_xlatb11.x) ? float(0.0) : u_xlat10.x;
                hlslcc_movcTemp.y = (u_xlatb11.z) ? float(0.0) : u_xlat10.y;
                u_xlat10 = hlslcc_movcTemp;
            }
            u_xlat10.xy = u_xlat10.xy * vec2(20.0, 20.0) + u_xlat2.xz;
            u_xlat10.xy = u_xlat10.xy * vec2(0.00625000009, 0.0017857143) + vec2(0.503125012, 0.000892857148);
            u_xlat10.xy = textureLod(_AreaTexture, u_xlat10.xy, 0.0).xy;
        } else {
            u_xlat10.x = float(0.0);
            u_xlat10.y = float(0.0);
        }
        u_xlat28 = _Metrics.x * 0.25 + vs_TEXCOORD0.x;
        u_xlat2.xy = (-_Metrics.xy);
        u_xlat2.z = 1.0;
        u_xlat12.x = u_xlat28;
        u_xlat12.y = vs_TEXCOORD0.y;
        u_xlat3.x = float(1.0);
        u_xlat12.z = float(-1.0);
        while(true){
#ifdef UNITY_ADRENO_ES3
            u_xlatb29 = !!(u_xlat12.z<7.0);
#else
            u_xlatb29 = u_xlat12.z<7.0;
#endif
#ifdef UNITY_ADRENO_ES3
            u_xlatb4 = !!(0.899999976<u_xlat3.x);
#else
            u_xlatb4 = 0.899999976<u_xlat3.x;
#endif
            u_xlatb29 = u_xlatb29 && u_xlatb4;
            if(!u_xlatb29){break;}
            u_xlat0.xyz = u_xlat2.xyz + u_xlat12.xyz;
            u_xlat10_4.xy = textureLod(_ColorTexture, u_xlat0.xyw, 0.0).xy;
            u_xlat29 = u_xlat10_4.x * 5.0 + -3.75;
            u_xlat29 = abs(u_xlat29) * u_xlat10_4.x;
            u_xlat5.x = roundEven(u_xlat29);
            u_xlat5.y = roundEven(u_xlat10_4.y);
            u_xlat3.x = dot(u_xlat5.xy, vec2(0.5, 0.5));
            u_xlat12.xyz = u_xlat0.xyz;
        }
        u_xlat2.x = u_xlat12.z;
        u_xlat4.xy = _Metrics.xy * vec2(1.0, 0.0) + vs_TEXCOORD0.xy;
        u_xlat4.w = u_xlat0.w;
        u_xlat29 = textureLod(_ColorTexture, u_xlat4.xyw, 0.0).x;
#ifdef UNITY_ADRENO_ES3
        u_xlatb29 = !!(0.0<u_xlat29);
#else
        u_xlatb29 = 0.0<u_xlat29;
#endif
        if(u_xlatb29){
            u_xlat5.xy = _Metrics.xy;
            u_xlat5.z = 1.0;
            u_xlat4.x = u_xlat28;
            u_xlat4.y = vs_TEXCOORD0.y;
            u_xlat4.z = -1.0;
            u_xlat3.y = float(1.0);
            u_xlat21.x = float(0.0);
            while(true){
#ifdef UNITY_ADRENO_ES3
                u_xlatb29 = !!(u_xlat4.z<7.0);
#else
                u_xlatb29 = u_xlat4.z<7.0;
#endif
#ifdef UNITY_ADRENO_ES3
                u_xlatb32 = !!(0.899999976<u_xlat3.y);
#else
                u_xlatb32 = 0.899999976<u_xlat3.y;
#endif
                u_xlatb29 = u_xlatb29 && u_xlatb32;
                if(!u_xlatb29){break;}
                u_xlat4.xyz = u_xlat5.xyz + u_xlat4.xyz;
                u_xlat10_6.xy = textureLod(_ColorTexture, u_xlat4.xyw, 0.0).xy;
                u_xlat29 = u_xlat10_6.x * 5.0 + -3.75;
                u_xlat29 = abs(u_xlat29) * u_xlat10_6.x;
                u_xlat21.y = roundEven(u_xlat29);
                u_xlat21.x = roundEven(u_xlat10_6.y);
                u_xlat3.y = dot(u_xlat21.yx, vec2(0.5, 0.5));
            }
#ifdef UNITY_ADRENO_ES3
            u_xlatb28 = !!(0.899999976<u_xlat21.x);
#else
            u_xlatb28 = 0.899999976<u_xlat21.x;
#endif
            u_xlat28 = u_xlatb28 ? 1.0 : float(0.0);
            u_xlat2.z = u_xlat28 + u_xlat4.z;
        } else {
            u_xlat2.z = 0.0;
            u_xlat3.y = 0.0;
        }
        u_xlat28 = u_xlat2.z + u_xlat2.x;
#ifdef UNITY_ADRENO_ES3
        u_xlatb28 = !!(2.0<u_xlat28);
#else
        u_xlatb28 = 2.0<u_xlat28;
#endif
        if(u_xlatb28){
            u_xlat2.y = (-u_xlat2.x);
            u_xlat5 = u_xlat2.yyzz * _Metrics.xyxy + vs_TEXCOORD0.xyxy;
            u_xlat6 = _Metrics.xyxy * vec4(-1.0, 0.0, 0.0, -1.0) + u_xlat5.xyxy;
            u_xlat7.xy = u_xlat6.xy;
            u_xlat7.z = u_xlat4.w;
            u_xlat10_8.x = textureLod(_ColorTexture, u_xlat7.xyz, 0.0).y;
            u_xlat7.xy = u_xlat6.zw;
            u_xlat10_8.z = textureLod(_ColorTexture, u_xlat7.xyz, 0.0).x;
            u_xlat7.xy = _Metrics.xy * vec2(1.0, 0.0) + u_xlat5.zw;
            u_xlat10_8.yw = textureLod(_ColorTexture, u_xlat7.xyz, 0.0).yx;
            u_xlat11.xz = u_xlat10_8.xy * vec2(2.0, 2.0) + u_xlat10_8.zw;
            u_xlatb3.xy = greaterThanEqual(u_xlat3.xyxx, vec4(0.899999976, 0.899999976, 0.0, 0.0)).xy;
            {
                vec3 hlslcc_movcTemp = u_xlat11;
                hlslcc_movcTemp.x = (u_xlatb3.x) ? float(0.0) : u_xlat11.x;
                hlslcc_movcTemp.z = (u_xlatb3.y) ? float(0.0) : u_xlat11.z;
                u_xlat11 = hlslcc_movcTemp;
            }
            u_xlat2.xy = u_xlat11.xz * vec2(20.0, 20.0) + u_xlat2.xz;
            u_xlat2.xy = u_xlat2.xy * vec2(0.00625000009, 0.0017857143) + vec2(0.503125012, 0.000892857148);
            u_xlat10_2.xy = textureLod(_AreaTexture, u_xlat2.xy, 0.0).xy;
            u_xlat10.xy = u_xlat10.xy + u_xlat10_2.yx;
        }
#ifdef UNITY_ADRENO_ES3
        u_xlatb28 = !!((-u_xlat10.y)==u_xlat10.x);
#else
        u_xlatb28 = (-u_xlat10.y)==u_xlat10.x;
#endif
        if(u_xlatb28){
            u_xlat2.xy = vs_TEXCOORD2.xy;
            u_xlat2.z = 1.0;
            u_xlat10_3.x = 0.0;
            while(true){
#ifdef UNITY_ADRENO_ES3
                u_xlatb28 = !!(vs_TEXCOORD4.x<u_xlat2.x);
#else
                u_xlatb28 = vs_TEXCOORD4.x<u_xlat2.x;
#endif
#ifdef UNITY_ADRENO_ES3
                u_xlatb29 = !!(0.828100026<u_xlat2.z);
#else
                u_xlatb29 = 0.828100026<u_xlat2.z;
#endif
                u_xlatb28 = u_xlatb28 && u_xlatb29;
#ifdef UNITY_ADRENO_ES3
                u_xlatb29 = !!(u_xlat10_3.x==0.0);
#else
                u_xlatb29 = u_xlat10_3.x==0.0;
#endif
                u_xlatb28 = u_xlatb28 && u_xlatb29;
                if(!u_xlatb28){break;}
                u_xlat4.xy = u_xlat2.xy;
                u_xlat10_3.xy = textureLod(_ColorTexture, u_xlat4.xyw, 0.0).xy;
                u_xlat2.xy = _Metrics.xy * vec2(-2.0, -0.0) + u_xlat2.xy;
                u_xlat2.z = u_xlat10_3.y;
            }
            u_xlat10_3.yz = u_xlat2.xz;
            u_xlat2.xy = u_xlat10_3.xz * vec2(0.5, -2.0) + vec2(0.0078125, 2.03125);
            u_xlat10_28 = textureLod(_SearchTexture, u_xlat2.xy, 0.0).w;
            u_xlat28 = u_xlat10_28 * -2.00787401 + 3.25;
            u_xlat4.x = _Metrics.x * u_xlat28 + u_xlat10_3.y;
            u_xlat4.y = vs_TEXCOORD3.y;
            u_xlat10_2.x = textureLod(_ColorTexture, u_xlat4.xyw, 0.0).x;
            u_xlat3.z = u_xlat4.w;
            u_xlat5.xy = vs_TEXCOORD2.zw;
            u_xlat5.z = 1.0;
            u_xlat10_6.x = 0.0;
            while(true){
#ifdef UNITY_ADRENO_ES3
                u_xlatb28 = !!(u_xlat5.x<vs_TEXCOORD4.y);
#else
                u_xlatb28 = u_xlat5.x<vs_TEXCOORD4.y;
#endif
#ifdef UNITY_ADRENO_ES3
                u_xlatb20 = !!(0.828100026<u_xlat5.z);
#else
                u_xlatb20 = 0.828100026<u_xlat5.z;
#endif
                u_xlatb28 = u_xlatb28 && u_xlatb20;
#ifdef UNITY_ADRENO_ES3
                u_xlatb20 = !!(u_xlat10_6.x==0.0);
#else
                u_xlatb20 = u_xlat10_6.x==0.0;
#endif
                u_xlatb28 = u_xlatb28 && u_xlatb20;
                if(!u_xlatb28){break;}
                u_xlat3.xy = u_xlat5.xy;
                u_xlat10_6.xy = textureLod(_ColorTexture, u_xlat3.xyz, 0.0).xy;
                u_xlat5.xy = _Metrics.xy * vec2(2.0, 0.0) + u_xlat5.xy;
                u_xlat5.z = u_xlat10_6.y;
            }
            u_xlat10_6.yz = u_xlat5.xz;
            u_xlat20.xy = u_xlat10_6.xz * vec2(0.5, -2.0) + vec2(0.5234375, 2.03125);
            u_xlat10_28 = textureLod(_SearchTexture, u_xlat20.xy, 0.0).w;
            u_xlat28 = u_xlat10_28 * -2.00787401 + 3.25;
            u_xlat4.z = (-_Metrics.x) * u_xlat28 + u_xlat10_6.y;
            u_xlat5 = _Metrics.zzzz * u_xlat4.zxzx + (-vs_TEXCOORD1.xxxx);
            u_xlat5 = roundEven(u_xlat5);
            u_xlat20.xy = sqrt(abs(u_xlat5.wz));
            u_xlat3.xy = _Metrics.xy * vec2(1.0, 0.0) + u_xlat4.zy;
            u_xlat10_2.y = textureLod(_ColorTexture, u_xlat3.xyz, 0.0).x;
            u_xlat2.xy = u_xlat10_2.xy * vec2(4.0, 4.0);
            u_xlat2.xy = roundEven(u_xlat2.xy);
            u_xlat2.xy = u_xlat2.xy * vec2(16.0, 16.0) + u_xlat20.xy;
            u_xlat2.xy = u_xlat2.xy * vec2(0.00625000009, 0.0017857143) + vec2(0.00312500005, 0.000892857148);
            u_xlat10_2.xy = textureLod(_AreaTexture, u_xlat2.xy, 0.0).xy;
            u_xlatb5 = greaterThanEqual(abs(u_xlat5), abs(u_xlat5.wzwz));
            u_xlat5.x = u_xlatb5.x ? float(1.0) : 0.0;
            u_xlat5.y = u_xlatb5.y ? float(1.0) : 0.0;
            u_xlat5.z = u_xlatb5.z ? float(0.75) : 0.0;
            u_xlat5.w = u_xlatb5.w ? float(0.75) : 0.0;
;
            u_xlat28 = u_xlat5.y + u_xlat5.x;
            u_xlat20.xy = u_xlat5.zw / vec2(u_xlat28);
            u_xlat4.yw = vs_TEXCOORD0.yy;
            u_xlat3.xy = _Metrics.xy * vec2(0.0, 1.0) + u_xlat4.xy;
            u_xlat10_28 = textureLod(_ColorTexture, u_xlat3.xyz, 0.0).x;
            u_xlat28 = (-u_xlat20.x) * u_xlat10_28 + 1.0;
            u_xlat3.xy = u_xlat4.zw + _Metrics.xy;
            u_xlat10_30 = textureLod(_ColorTexture, u_xlat3.xyz, 0.0).x;
            u_xlat5.x = (-u_xlat20.y) * u_xlat10_30 + u_xlat28;
#ifdef UNITY_ADRENO_ES3
            u_xlat5.x = min(max(u_xlat5.x, 0.0), 1.0);
#else
            u_xlat5.x = clamp(u_xlat5.x, 0.0, 1.0);
#endif
            u_xlat3.xy = _Metrics.xy * vec2(0.0, -2.0) + u_xlat4.xy;
            u_xlat10_28 = textureLod(_ColorTexture, u_xlat3.xyz, 0.0).x;
            u_xlat28 = (-u_xlat20.x) * u_xlat10_28 + 1.0;
            u_xlat3.xy = _Metrics.xy * vec2(1.0, -2.0) + u_xlat4.zw;
            u_xlat10_20 = textureLod(_ColorTexture, u_xlat3.xyz, 0.0).x;
            u_xlat5.y = (-u_xlat20.y) * u_xlat10_20 + u_xlat28;
#ifdef UNITY_ADRENO_ES3
            u_xlat5.y = min(max(u_xlat5.y, 0.0), 1.0);
#else
            u_xlat5.y = clamp(u_xlat5.y, 0.0, 1.0);
#endif
            SV_Target0.xy = u_xlat10_2.xy * u_xlat5.xy;
        } else {
            SV_Target0.xy = u_xlat10.xy;
            u_xlat1.x = 0.0;
        }
    } else {
        SV_Target0.xy = vec2(0.0, 0.0);
    }
#ifdef UNITY_ADRENO_ES3
    u_xlatb1 = !!(0.0<u_xlat1.x);
#else
    u_xlatb1 = 0.0<u_xlat1.x;
#endif
    if(u_xlatb1){
        u_xlat1.xy = vs_TEXCOORD3.xy;
        u_xlat1.z = 1.0;
        u_xlat10_2.x = 0.0;
        while(true){
#ifdef UNITY_ADRENO_ES3
            u_xlatb28 = !!(vs_TEXCOORD4.z<u_xlat1.y);
#else
            u_xlatb28 = vs_TEXCOORD4.z<u_xlat1.y;
#endif
#ifdef UNITY_ADRENO_ES3
            u_xlatb29 = !!(0.828100026<u_xlat1.z);
#else
            u_xlatb29 = 0.828100026<u_xlat1.z;
#endif
            u_xlatb28 = u_xlatb28 && u_xlatb29;
#ifdef UNITY_ADRENO_ES3
            u_xlatb29 = !!(u_xlat10_2.x==0.0);
#else
            u_xlatb29 = u_xlat10_2.x==0.0;
#endif
            u_xlatb28 = u_xlatb28 && u_xlatb29;
            if(!u_xlatb28){break;}
            u_xlat0.xy = u_xlat1.xy;
            u_xlat10_2.xy = textureLod(_ColorTexture, u_xlat0.xyw, 0.0).yx;
            u_xlat1.xy = _Metrics.xy * vec2(-0.0, -2.0) + u_xlat1.xy;
            u_xlat1.z = u_xlat10_2.y;
        }
        u_xlat10_2.yz = u_xlat1.yz;
        u_xlat1.xy = u_xlat10_2.xz * vec2(0.5, -2.0) + vec2(0.0078125, 2.03125);
        u_xlat10_1.x = textureLod(_SearchTexture, u_xlat1.xy, 0.0).w;
        u_xlat1.x = u_xlat10_1.x * -2.00787401 + 3.25;
        u_xlat0.x = _Metrics.y * u_xlat1.x + u_xlat10_2.y;
        u_xlat0.y = vs_TEXCOORD2.x;
        u_xlat10_1.x = textureLod(_ColorTexture, u_xlat0.yxw, 0.0).y;
        u_xlat2.z = u_xlat0.w;
        u_xlat3.xy = vs_TEXCOORD3.zw;
        u_xlat3.z = 1.0;
        u_xlat10_4.x = 0.0;
        while(true){
#ifdef UNITY_ADRENO_ES3
            u_xlatb19 = !!(u_xlat3.y<vs_TEXCOORD4.w);
#else
            u_xlatb19 = u_xlat3.y<vs_TEXCOORD4.w;
#endif
#ifdef UNITY_ADRENO_ES3
            u_xlatb28 = !!(0.828100026<u_xlat3.z);
#else
            u_xlatb28 = 0.828100026<u_xlat3.z;
#endif
            u_xlatb19 = u_xlatb28 && u_xlatb19;
#ifdef UNITY_ADRENO_ES3
            u_xlatb28 = !!(u_xlat10_4.x==0.0);
#else
            u_xlatb28 = u_xlat10_4.x==0.0;
#endif
            u_xlatb19 = u_xlatb28 && u_xlatb19;
            if(!u_xlatb19){break;}
            u_xlat2.xy = u_xlat3.xy;
            u_xlat10_4.xy = textureLod(_ColorTexture, u_xlat2.xyz, 0.0).yx;
            u_xlat3.xy = _Metrics.xy * vec2(0.0, 2.0) + u_xlat3.xy;
            u_xlat3.z = u_xlat10_4.y;
        }
        u_xlat10_4.yz = u_xlat3.yz;
        u_xlat19.xy = u_xlat10_4.xz * vec2(0.5, -2.0) + vec2(0.5234375, 2.03125);
        u_xlat10_19 = textureLod(_SearchTexture, u_xlat19.xy, 0.0).w;
        u_xlat19.x = u_xlat10_19 * -2.00787401 + 3.25;
        u_xlat0.z = (-_Metrics.y) * u_xlat19.x + u_xlat10_4.y;
        u_xlat3 = _Metrics.wwww * u_xlat0.zxzx + (-vs_TEXCOORD1.yyyy);
        u_xlat3 = roundEven(u_xlat3);
        u_xlat19.xy = sqrt(abs(u_xlat3.wz));
        u_xlat2.xy = _Metrics.xy * vec2(0.0, 1.0) + u_xlat0.yz;
        u_xlat10_1.y = textureLod(_ColorTexture, u_xlat2.xyz, 0.0).y;
        u_xlat1.xy = u_xlat10_1.xy * vec2(4.0, 4.0);
        u_xlat1.xy = roundEven(u_xlat1.xy);
        u_xlat1.xy = u_xlat1.xy * vec2(16.0, 16.0) + u_xlat19.xy;
        u_xlat1.xy = u_xlat1.xy * vec2(0.00625000009, 0.0017857143) + vec2(0.00312500005, 0.000892857148);
        u_xlat10_1.xy = textureLod(_AreaTexture, u_xlat1.xy, 0.0).xy;
        u_xlatb3 = greaterThanEqual(abs(u_xlat3), abs(u_xlat3.wzwz));
        u_xlat3.x = u_xlatb3.x ? float(1.0) : 0.0;
        u_xlat3.y = u_xlatb3.y ? float(1.0) : 0.0;
        u_xlat3.z = u_xlatb3.z ? float(0.75) : 0.0;
        u_xlat3.w = u_xlatb3.w ? float(0.75) : 0.0;
;
        u_xlat19.x = u_xlat3.y + u_xlat3.x;
        u_xlat19.xy = u_xlat3.zw / u_xlat19.xx;
        u_xlat0.yw = vs_TEXCOORD0.xx;
        u_xlat2.xy = _Metrics.xy * vec2(1.0, 0.0) + u_xlat0.yx;
        u_xlat10_29 = textureLod(_ColorTexture, u_xlat2.xyz, 0.0).y;
        u_xlat29 = (-u_xlat19.x) * u_xlat10_29 + 1.0;
        u_xlat2.xy = u_xlat0.wz + _Metrics.xy;
        u_xlat10_3.x = textureLod(_ColorTexture, u_xlat2.xyz, 0.0).y;
        u_xlat21.x = (-u_xlat19.y) * u_xlat10_3.x + u_xlat29;
#ifdef UNITY_ADRENO_ES3
        u_xlat21.x = min(max(u_xlat21.x, 0.0), 1.0);
#else
        u_xlat21.x = clamp(u_xlat21.x, 0.0, 1.0);
#endif
        u_xlat2.xy = _Metrics.xy * vec2(-2.0, 0.0) + u_xlat0.yx;
        u_xlat10_0 = textureLod(_ColorTexture, u_xlat2.xyz, 0.0).y;
        u_xlat0.x = (-u_xlat19.x) * u_xlat10_0 + 1.0;
        u_xlat2.xy = _Metrics.xy * vec2(-2.0, 1.0) + u_xlat0.wz;
        u_xlat10_9 = textureLod(_ColorTexture, u_xlat2.xyz, 0.0).y;
        u_xlat21.y = (-u_xlat19.y) * u_xlat10_9 + u_xlat0.x;
#ifdef UNITY_ADRENO_ES3
        u_xlat21.y = min(max(u_xlat21.y, 0.0), 1.0);
#else
        u_xlat21.y = clamp(u_xlat21.y, 0.0, 1.0);
#endif
        SV_Target0.zw = u_xlat10_1.xy * u_xlat21.xy;
    } else {
        SV_Target0.zw = vec2(0.0, 0.0);
    }
    return;
}

#endif
                             $Globals         _Metrics                             $GlobalsP         _Metrics                  @      _FullscreenProjMat                              UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _ColorTexture               
      _AreaTexture                _SearchTexture                  UnityStereoEyeIndices                  