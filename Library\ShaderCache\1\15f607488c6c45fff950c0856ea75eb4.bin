  <Q                         STEREO_MULTIVIEW_ON 
   _DEPTH_MSAA_4      _USE_DRAW_PROCEDURAL    A  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBiasRt;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLS<PERSON><PERSON>_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
highp  vec4 phase0_Output0_1;
out highp float vs_BLENDWEIGHT0;
vec2 u_xlat0;
int u_xlati0;
uvec2 u_xlatu0;
vec4 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    u_xlat0.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.y = u_xlat0.y * _ScaleBiasRt.x;
    gl_Position.x = u_xlat0.x;
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    u_xlat1.w = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    phase0_Output0_1.xyz = u_xlat1.xzw;
vs_TEXCOORD0 = phase0_Output0_1.xy;
vs_BLENDWEIGHT0 = phase0_Output0_1.z;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#extension GL_OES_texture_storage_multisample_2d_array : require

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _CameraDepthAttachment_TexelSize;
UNITY_LOCATION(0) uniform highp sampler2DMSArray _CameraDepthAttachment;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
vec2 u_xlat0;
uvec4 u_xlatu0;
float u_xlat1;
float u_xlat2;
float u_xlat3;
void main()
{
    u_xlat0.xy = vs_TEXCOORD0.xy * _CameraDepthAttachment_TexelSize.zw;
    u_xlatu0.xy =  uvec2(ivec2(u_xlat0.xy));
    u_xlatu0.z = uint(vs_BLENDWEIGHT0);
    u_xlatu0.w = uint(0u);
    u_xlat1 = texelFetch(_CameraDepthAttachment, ivec3(u_xlatu0.xyz), 0).x;
    u_xlat1 = max(u_xlat1, 0.0);
    u_xlat3 = texelFetch(_CameraDepthAttachment, ivec3(u_xlatu0.xyz), 1).x;
    u_xlat1 = max(u_xlat1, u_xlat3);
    u_xlat3 = texelFetch(_CameraDepthAttachment, ivec3(u_xlatu0.xyz), 2).x;
    u_xlat0.x = texelFetch(_CameraDepthAttachment, ivec3(u_xlatu0.xyz), 3).x;
    u_xlat2 = max(u_xlat1, u_xlat3);
    gl_FragDepth = max(u_xlat2, u_xlat0.x);
    return;
}

#endif
                                 $Globals          _CameraDepthAttachment_TexelSize                             $Globals         _ScaleBiasRt                             UnityStereoEyeIndices             unity_StereoEyeIndices                                 _CameraDepthAttachment                    UnityStereoEyeIndices                  