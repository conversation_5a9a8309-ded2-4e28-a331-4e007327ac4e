x  <Q                         _USE_DRAW_PROCEDURAL   	   _USE_RGBM   b  #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
out highp vec2 vs_TEXCOORD0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceTex_TexelSize;
UNITY_LOCATION(0) uniform mediump sampler2D _SourceTex;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec4 u_xlat10_0;
mediump vec3 u_xlat16_1;
vec4 u_xlat2;
mediump vec4 u_xlat10_2;
mediump vec4 u_xlat10_3;
mediump vec3 u_xlat16_4;
mediump vec3 u_xlat16_5;
mediump vec3 u_xlat16_7;
void main()
{
    u_xlat10_0 = texture(_SourceTex, vs_TEXCOORD0.xy);
    u_xlat16_1.xyz = u_xlat10_0.xyz * u_xlat10_0.xyz;
    u_xlat16_1.xyz = u_xlat10_0.www * u_xlat16_1.xyz;
    u_xlat0.x = 0.0;
    u_xlat0.yw = _SourceTex_TexelSize.yy * vec2(3.23076916, 1.38461542);
    u_xlat2 = (-u_xlat0.xyxw) + vs_TEXCOORD0.xyxy;
    u_xlat0 = u_xlat0.xwxy + vs_TEXCOORD0.xyxy;
    u_xlat10_3 = texture(_SourceTex, u_xlat2.zw);
    u_xlat10_2 = texture(_SourceTex, u_xlat2.xy);
    u_xlat16_4.xyz = u_xlat10_3.xyz * u_xlat10_3.xyz;
    u_xlat16_4.xyz = u_xlat10_3.www * u_xlat16_4.xyz;
    u_xlat16_4.xyz = u_xlat16_4.xyz * vec3(2.52972984, 2.52972984, 2.52972984);
    u_xlat16_5.xyz = u_xlat10_2.xyz * u_xlat10_2.xyz;
    u_xlat16_5.xyz = u_xlat10_2.www * u_xlat16_5.xyz;
    u_xlat16_4.xyz = u_xlat16_5.xyz * vec3(0.562162161, 0.562162161, 0.562162161) + u_xlat16_4.xyz;
    u_xlat16_1.xyz = u_xlat16_1.xyz * vec3(1.81621623, 1.81621623, 1.81621623) + u_xlat16_4.xyz;
    u_xlat10_2 = texture(_SourceTex, u_xlat0.xy);
    u_xlat10_0 = texture(_SourceTex, u_xlat0.zw);
    u_xlat16_4.xyz = u_xlat10_2.xyz * u_xlat10_2.xyz;
    u_xlat16_4.xyz = u_xlat10_2.www * u_xlat16_4.xyz;
    u_xlat16_1.xyz = u_xlat16_4.xyz * vec3(2.52972984, 2.52972984, 2.52972984) + u_xlat16_1.xyz;
    u_xlat16_4.xyz = u_xlat10_0.xyz * u_xlat10_0.xyz;
    u_xlat16_4.xyz = u_xlat10_0.www * u_xlat16_4.xyz;
    u_xlat16_1.xyz = u_xlat16_4.xyz * vec3(0.562162161, 0.562162161, 0.562162161) + u_xlat16_1.xyz;
    u_xlat0.xyz = u_xlat16_1.xyz * vec3(0.125, 0.125, 0.125);
    u_xlat16_1.x = max(u_xlat0.y, u_xlat0.x);
    u_xlat16_7.x = max(u_xlat0.z, 9.99999975e-06);
    u_xlat16_1.x = max(u_xlat16_7.x, u_xlat16_1.x);
    u_xlat16_1.x = u_xlat16_1.x * 255.0;
    u_xlat16_1.x = ceil(u_xlat16_1.x);
    u_xlat16_1.x = u_xlat16_1.x * 0.00392156886;
    u_xlat16_7.xyz = u_xlat0.xyz / u_xlat16_1.xxx;
    SV_Target0.w = u_xlat16_1.x;
    SV_Target0.xyz = sqrt(u_xlat16_7.xyz);
    return;
}

#endif
                                $Globals         _SourceTex_TexelSize                             $Globals      
   _ScaleBias                               
   _SourceTex                     