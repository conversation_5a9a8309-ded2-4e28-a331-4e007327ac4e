\[  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL      _CHROMATIC_ABERRATION      _DISTORTION 
   _TONEMAP_ACES   .X  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	vec4 _Distortion_Params1;
uniform 	vec4 _Distortion_Params2;
uniform 	float _Chroma_Params;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(2) uniform mediump sampler2D _UserLut;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec3 u_xlat10_0;
uint u_xlatu0;
bvec3 u_xlatb0;
vec3 u_xlat1;
mediump vec3 u_xlat16_1;
vec3 u_xlat2;
mediump vec3 u_xlat10_2;
bvec2 u_xlatb2;
vec4 u_xlat3;
vec4 u_xlat4;
vec3 u_xlat5;
mediump vec3 u_xlat16_6;
mediump vec3 u_xlat16_7;
mediump vec3 u_xlat16_8;
vec3 u_xlat9;
mediump vec3 u_xlat10_9;
bool u_xlatb9;
float u_xlat10;
float u_xlat11;
bool u_xlatb11;
float u_xlat12;
mediump vec3 u_xlat16_15;
mediump vec2 u_xlat16_16;
bool u_xlatb18;
float u_xlat19;
bool u_xlatb19;
float u_xlat20;
bool u_xlatb20;
bool u_xlatb21;
mediump float u_xlat16_24;
mediump float u_xlat16_25;
float u_xlat27;
bool u_xlatb27;
float u_xlat28;
float u_xlat29;
bool u_xlatb29;
bool u_xlatb32;
mediump float u_xlat16_33;
mediump float u_xlat16_34;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat9.xy = vs_TEXCOORD0.xy + vec2(-0.5, -0.5);
    u_xlat1.xy = u_xlat9.xy * _Distortion_Params2.zz + vec2(0.5, 0.5);
    u_xlat9.xy = u_xlat9.xy * _Distortion_Params2.zz + (-_Distortion_Params1.xy);
    u_xlat9.xy = u_xlat9.xy * _Distortion_Params1.zw;
    u_xlat27 = dot(u_xlat9.xy, u_xlat9.xy);
    u_xlat27 = sqrt(u_xlat27);
#ifdef UNITY_ADRENO_ES3
    u_xlatb19 = !!(0.0<_Distortion_Params2.w);
#else
    u_xlatb19 = 0.0<_Distortion_Params2.w;
#endif
    if(u_xlatb19){
        u_xlat2.xy = vec2(u_xlat27) * _Distortion_Params2.xy;
        u_xlat3.x = cos(u_xlat2.x);
        u_xlat2.x = sin(u_xlat2.x);
        u_xlat28 = u_xlat2.x / u_xlat3.x;
        u_xlat2.x = float(1.0) / float(u_xlat2.y);
        u_xlat28 = u_xlat28 * u_xlat2.x + -1.0;
        u_xlat2.xy = u_xlat9.xy * vec2(u_xlat28) + u_xlat1.xy;
    } else {
        u_xlat28 = float(1.0) / float(u_xlat27);
        u_xlat28 = u_xlat28 * _Distortion_Params2.x;
        u_xlat27 = u_xlat27 * _Distortion_Params2.y;
        u_xlat29 = min(abs(u_xlat27), 1.0);
        u_xlat3.x = max(abs(u_xlat27), 1.0);
        u_xlat3.x = float(1.0) / u_xlat3.x;
        u_xlat29 = u_xlat29 * u_xlat3.x;
        u_xlat3.x = u_xlat29 * u_xlat29;
        u_xlat12 = u_xlat3.x * 0.0208350997 + -0.0851330012;
        u_xlat12 = u_xlat3.x * u_xlat12 + 0.180141002;
        u_xlat12 = u_xlat3.x * u_xlat12 + -0.330299497;
        u_xlat3.x = u_xlat3.x * u_xlat12 + 0.999866009;
        u_xlat12 = u_xlat29 * u_xlat3.x;
#ifdef UNITY_ADRENO_ES3
        u_xlatb21 = !!(1.0<abs(u_xlat27));
#else
        u_xlatb21 = 1.0<abs(u_xlat27);
#endif
        u_xlat12 = u_xlat12 * -2.0 + 1.57079637;
        u_xlat12 = u_xlatb21 ? u_xlat12 : float(0.0);
        u_xlat29 = u_xlat29 * u_xlat3.x + u_xlat12;
        u_xlat27 = min(u_xlat27, 1.0);
#ifdef UNITY_ADRENO_ES3
        u_xlatb27 = !!(u_xlat27<(-u_xlat27));
#else
        u_xlatb27 = u_xlat27<(-u_xlat27);
#endif
        u_xlat27 = (u_xlatb27) ? (-u_xlat29) : u_xlat29;
        u_xlat27 = u_xlat28 * u_xlat27 + -1.0;
        u_xlat2.xy = u_xlat9.xy * vec2(u_xlat27) + u_xlat1.xy;
    }
    u_xlat3 = vs_TEXCOORD0.xyxy * vec4(2.0, 2.0, 2.0, 2.0) + vec4(-1.0, -1.0, -1.0, -1.0);
    u_xlat9.x = dot(u_xlat3.zw, u_xlat3.zw);
    u_xlat3 = u_xlat9.xxxx * u_xlat3;
    u_xlat3 = u_xlat3 * vec4(_Chroma_Params);
    u_xlat2.z = float(u_xlatu0);
    u_xlat10_0.x = texture(_SourceTex, u_xlat2.xyz).x;
    u_xlat3 = u_xlat3 * vec4(-0.333333343, -0.333333343, -0.666666687, -0.666666687) + vs_TEXCOORD0.xyxy;
    u_xlat3 = u_xlat3 + vec4(-0.5, -0.5, -0.5, -0.5);
    u_xlat4 = u_xlat3 * _Distortion_Params2.zzzz + vec4(0.5, 0.5, 0.5, 0.5);
    u_xlat3 = u_xlat3 * _Distortion_Params2.zzzz + (-_Distortion_Params1.xyxy);
    u_xlat3 = u_xlat3 * _Distortion_Params1.zwzw;
    u_xlat27 = dot(u_xlat3.xy, u_xlat3.xy);
    u_xlat27 = sqrt(u_xlat27);
    if(u_xlatb19){
        u_xlat1.xy = vec2(u_xlat27) * _Distortion_Params2.xy;
        u_xlat5.x = cos(u_xlat1.x);
        u_xlat1.x = sin(u_xlat1.x);
        u_xlat1.x = u_xlat1.x / u_xlat5.x;
        u_xlat10 = float(1.0) / float(u_xlat1.y);
        u_xlat1.x = u_xlat1.x * u_xlat10 + -1.0;
        u_xlat5.xy = u_xlat3.xy * u_xlat1.xx + u_xlat4.xy;
    } else {
        u_xlat1.x = float(1.0) / float(u_xlat27);
        u_xlat1.x = u_xlat1.x * _Distortion_Params2.x;
        u_xlat27 = u_xlat27 * _Distortion_Params2.y;
        u_xlat10 = min(abs(u_xlat27), 1.0);
        u_xlat28 = max(abs(u_xlat27), 1.0);
        u_xlat28 = float(1.0) / u_xlat28;
        u_xlat10 = u_xlat28 * u_xlat10;
        u_xlat28 = u_xlat10 * u_xlat10;
        u_xlat29 = u_xlat28 * 0.0208350997 + -0.0851330012;
        u_xlat29 = u_xlat28 * u_xlat29 + 0.180141002;
        u_xlat29 = u_xlat28 * u_xlat29 + -0.330299497;
        u_xlat28 = u_xlat28 * u_xlat29 + 0.999866009;
        u_xlat29 = u_xlat28 * u_xlat10;
#ifdef UNITY_ADRENO_ES3
        u_xlatb32 = !!(1.0<abs(u_xlat27));
#else
        u_xlatb32 = 1.0<abs(u_xlat27);
#endif
        u_xlat29 = u_xlat29 * -2.0 + 1.57079637;
        u_xlat29 = u_xlatb32 ? u_xlat29 : float(0.0);
        u_xlat10 = u_xlat10 * u_xlat28 + u_xlat29;
        u_xlat27 = min(u_xlat27, 1.0);
#ifdef UNITY_ADRENO_ES3
        u_xlatb27 = !!(u_xlat27<(-u_xlat27));
#else
        u_xlatb27 = u_xlat27<(-u_xlat27);
#endif
        u_xlat27 = (u_xlatb27) ? (-u_xlat10) : u_xlat10;
        u_xlat27 = u_xlat1.x * u_xlat27 + -1.0;
        u_xlat5.xy = u_xlat3.xy * vec2(u_xlat27) + u_xlat4.xy;
    }
    u_xlat5.z = u_xlat2.z;
    u_xlat10_0.y = texture(_SourceTex, u_xlat5.xyz).y;
    u_xlat27 = dot(u_xlat3.zw, u_xlat3.zw);
    u_xlat27 = sqrt(u_xlat27);
    if(u_xlatb19){
        u_xlat1.xy = vec2(u_xlat27) * _Distortion_Params2.xy;
        u_xlat3.x = cos(u_xlat1.x);
        u_xlat1.x = sin(u_xlat1.x);
        u_xlat1.x = u_xlat1.x / u_xlat3.x;
        u_xlat10 = float(1.0) / float(u_xlat1.y);
        u_xlat1.x = u_xlat1.x * u_xlat10 + -1.0;
        u_xlat5.xy = u_xlat3.zw * u_xlat1.xx + u_xlat4.zw;
    } else {
        u_xlat1.x = float(1.0) / float(u_xlat27);
        u_xlat1.x = u_xlat1.x * _Distortion_Params2.x;
        u_xlat27 = u_xlat27 * _Distortion_Params2.y;
        u_xlat10 = min(abs(u_xlat27), 1.0);
        u_xlat19 = max(abs(u_xlat27), 1.0);
        u_xlat19 = float(1.0) / u_xlat19;
        u_xlat10 = u_xlat19 * u_xlat10;
        u_xlat19 = u_xlat10 * u_xlat10;
        u_xlat28 = u_xlat19 * 0.0208350997 + -0.0851330012;
        u_xlat28 = u_xlat19 * u_xlat28 + 0.180141002;
        u_xlat28 = u_xlat19 * u_xlat28 + -0.330299497;
        u_xlat19 = u_xlat19 * u_xlat28 + 0.999866009;
        u_xlat28 = u_xlat19 * u_xlat10;
#ifdef UNITY_ADRENO_ES3
        u_xlatb20 = !!(1.0<abs(u_xlat27));
#else
        u_xlatb20 = 1.0<abs(u_xlat27);
#endif
        u_xlat28 = u_xlat28 * -2.0 + 1.57079637;
        u_xlat28 = u_xlatb20 ? u_xlat28 : float(0.0);
        u_xlat10 = u_xlat10 * u_xlat19 + u_xlat28;
        u_xlat27 = min(u_xlat27, 1.0);
#ifdef UNITY_ADRENO_ES3
        u_xlatb27 = !!(u_xlat27<(-u_xlat27));
#else
        u_xlatb27 = u_xlat27<(-u_xlat27);
#endif
        u_xlat27 = (u_xlatb27) ? (-u_xlat10) : u_xlat10;
        u_xlat27 = u_xlat1.x * u_xlat27 + -1.0;
        u_xlat5.xy = u_xlat3.zw * vec2(u_xlat27) + u_xlat4.zw;
    }
    u_xlat10_0.z = texture(_SourceTex, u_xlat5.xyz).z;
#ifdef UNITY_ADRENO_ES3
    u_xlatb27 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb27 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb27){
        u_xlat1.xy = u_xlat2.xy + (-_Vignette_Params2.xy);
        u_xlat1.yz = abs(u_xlat1.xy) * _Vignette_Params2.zz;
        u_xlat1.x = u_xlat1.y * _Vignette_Params1.w;
        u_xlat27 = dot(u_xlat1.xz, u_xlat1.xz);
        u_xlat27 = (-u_xlat27) + 1.0;
        u_xlat27 = max(u_xlat27, 0.0);
        u_xlat27 = log2(u_xlat27);
        u_xlat27 = u_xlat27 * _Vignette_Params2.w;
        u_xlat27 = exp2(u_xlat27);
        u_xlat1.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat1.xyz = vec3(u_xlat27) * u_xlat1.xyz + _Vignette_Params1.xyz;
        u_xlat1.xyz = u_xlat1.xyz * u_xlat10_0.xyz;
        u_xlat16_1.xyz = u_xlat1.xyz;
    } else {
        u_xlat16_1.xyz = u_xlat10_0.xyz;
    }
    u_xlat0.xyz = u_xlat16_1.xyz * _Lut_Params.www;
    u_xlat16_15.x = dot(vec3(0.439700991, 0.382977992, 0.177334994), u_xlat0.xyz);
    u_xlat16_15.y = dot(vec3(0.0897922963, 0.813422978, 0.0967615992), u_xlat0.xyz);
    u_xlat16_15.z = dot(vec3(0.0175439995, 0.111543998, 0.870703995), u_xlat0.xyz);
    u_xlat16_6.x = min(u_xlat16_15.y, u_xlat16_15.x);
    u_xlat16_6.x = min(u_xlat16_15.z, u_xlat16_6.x);
    u_xlat16_7.x = max(u_xlat16_15.y, u_xlat16_15.x);
    u_xlat16_7.x = max(u_xlat16_15.z, u_xlat16_7.x);
    u_xlat16_7.xy = max(u_xlat16_7.xx, vec2(9.99999975e-05, 0.00999999978));
    u_xlat16_6.x = max(u_xlat16_6.x, 9.99999975e-05);
    u_xlat16_6.x = (-u_xlat16_6.x) + u_xlat16_7.x;
    u_xlat16_6.x = u_xlat16_6.x / u_xlat16_7.y;
    u_xlat16_7.xyz = (-u_xlat16_15.yxz) + u_xlat16_15.zyx;
    u_xlat16_7.xy = u_xlat16_15.zy * u_xlat16_7.xy;
    u_xlat16_7.x = u_xlat16_7.y + u_xlat16_7.x;
    u_xlat16_7.x = u_xlat16_15.x * u_xlat16_7.z + u_xlat16_7.x;
    u_xlat0.x = max(u_xlat16_7.x, 0.0);
    u_xlat16_7.x = sqrt(u_xlat0.x);
    u_xlat16_16.x = u_xlat16_15.y + u_xlat16_15.z;
    u_xlat16_16.x = u_xlat16_15.x + u_xlat16_16.x;
    u_xlat16_7.x = u_xlat16_7.x * 1.75 + u_xlat16_16.x;
    u_xlat16_16.x = u_xlat16_7.x * 0.333333343;
    u_xlat0.x = u_xlat16_6.x + -0.400000006;
    u_xlat16_25 = u_xlat0.x * 2.5;
    u_xlat16_25 = -abs(u_xlat16_25) + 1.0;
    u_xlat16_25 = max(u_xlat16_25, 0.0);
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(u_xlat0.x>=0.0);
#else
    u_xlatb0.x = u_xlat0.x>=0.0;
#endif
    u_xlat0.x = (u_xlatb0.x) ? 1.0 : -1.0;
    u_xlat16_25 = (-u_xlat16_25) * u_xlat16_25 + 1.0;
    u_xlat0.x = u_xlat0.x * u_xlat16_25 + 1.0;
    u_xlat0.x = u_xlat0.x * 0.0250000004;
#ifdef UNITY_ADRENO_ES3
    u_xlatb9 = !!(0.159999996>=u_xlat16_7.x);
#else
    u_xlatb9 = 0.159999996>=u_xlat16_7.x;
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb18 = !!(u_xlat16_7.x>=0.479999989);
#else
    u_xlatb18 = u_xlat16_7.x>=0.479999989;
#endif
    u_xlat16_7.x = 0.0799999982 / u_xlat16_16.x;
    u_xlat16_7.x = u_xlat16_7.x + -0.5;
    u_xlat16_7.x = u_xlat0.x * u_xlat16_7.x;
    u_xlat16_7.x = (u_xlatb18) ? 0.0 : u_xlat16_7.x;
    u_xlat16_7.x = (u_xlatb9) ? u_xlat0.x : u_xlat16_7.x;
    u_xlat16_7.x = u_xlat16_7.x + 1.0;
    u_xlat0.yzw = u_xlat16_15.xyz * u_xlat16_7.xxx;
    u_xlatb2.xy = equal(u_xlat0.zwzz, u_xlat0.yzyy).xy;
    u_xlatb2.x = u_xlatb2.y && u_xlatb2.x;
    u_xlat16_24 = u_xlat16_15.y * u_xlat16_7.x + (-u_xlat0.w);
    u_xlat16_24 = u_xlat16_24 * 1.73205078;
    u_xlat16_16.x = u_xlat0.y * 2.0 + (-u_xlat0.z);
    u_xlat16_33 = (-u_xlat16_15.z) * u_xlat16_7.x + u_xlat16_16.x;
    u_xlat16_16.x = min(abs(u_xlat16_33), abs(u_xlat16_24));
    u_xlat16_25 = max(abs(u_xlat16_33), abs(u_xlat16_24));
    u_xlat16_25 = float(1.0) / u_xlat16_25;
    u_xlat16_16.x = u_xlat16_25 * u_xlat16_16.x;
    u_xlat16_25 = u_xlat16_16.x * u_xlat16_16.x;
    u_xlat11 = u_xlat16_25 * 0.0208350997 + -0.0851330012;
    u_xlat11 = u_xlat16_25 * u_xlat11 + 0.180141002;
    u_xlat11 = u_xlat16_25 * u_xlat11 + -0.330299497;
    u_xlat11 = u_xlat16_25 * u_xlat11 + 0.999866009;
    u_xlat20 = u_xlat11 * u_xlat16_16.x;
#ifdef UNITY_ADRENO_ES3
    u_xlatb29 = !!(abs(u_xlat16_33)<abs(u_xlat16_24));
#else
    u_xlatb29 = abs(u_xlat16_33)<abs(u_xlat16_24);
#endif
    u_xlat20 = u_xlat20 * -2.0 + 1.57079637;
    u_xlat20 = u_xlatb29 ? u_xlat20 : float(0.0);
    u_xlat11 = u_xlat16_16.x * u_xlat11 + u_xlat20;
#ifdef UNITY_ADRENO_ES3
    u_xlatb20 = !!(u_xlat16_33<(-u_xlat16_33));
#else
    u_xlatb20 = u_xlat16_33<(-u_xlat16_33);
#endif
    u_xlat20 = u_xlatb20 ? -3.14159274 : float(0.0);
    u_xlat11 = u_xlat20 + u_xlat11;
    u_xlat16_16.x = min(u_xlat16_33, u_xlat16_24);
    u_xlat16_24 = max(u_xlat16_33, u_xlat16_24);
#ifdef UNITY_ADRENO_ES3
    u_xlatb20 = !!(u_xlat16_16.x<(-u_xlat16_16.x));
#else
    u_xlatb20 = u_xlat16_16.x<(-u_xlat16_16.x);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb29 = !!(u_xlat16_24>=(-u_xlat16_24));
#else
    u_xlatb29 = u_xlat16_24>=(-u_xlat16_24);
#endif
    u_xlatb20 = u_xlatb29 && u_xlatb20;
    u_xlat11 = (u_xlatb20) ? (-u_xlat11) : u_xlat11;
    u_xlat16_24 = u_xlat11 * 57.2957802;
    u_xlat16_24 = (u_xlatb2.x) ? 0.0 : u_xlat16_24;
#ifdef UNITY_ADRENO_ES3
    u_xlatb2.x = !!(u_xlat16_24<0.0);
#else
    u_xlatb2.x = u_xlat16_24<0.0;
#endif
    u_xlat16_33 = u_xlat16_24 + 360.0;
    u_xlat16_24 = (u_xlatb2.x) ? u_xlat16_33 : u_xlat16_24;
#ifdef UNITY_ADRENO_ES3
    u_xlatb2.x = !!(u_xlat16_24<-180.0);
#else
    u_xlatb2.x = u_xlat16_24<-180.0;
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb11 = !!(180.0<u_xlat16_24);
#else
    u_xlatb11 = 180.0<u_xlat16_24;
#endif
    u_xlat16_16.xy = vec2(u_xlat16_24) + vec2(360.0, -360.0);
    u_xlat16_24 = (u_xlatb11) ? u_xlat16_16.y : u_xlat16_24;
    u_xlat16_24 = (u_xlatb2.x) ? u_xlat16_16.x : u_xlat16_24;
    u_xlat2.x = u_xlat16_24 * 0.0148148146;
    u_xlat2.x = -abs(u_xlat2.x) + 1.0;
    u_xlat2.x = max(u_xlat2.x, 0.0);
    u_xlat11 = u_xlat2.x * -2.0 + 3.0;
    u_xlat2.x = u_xlat2.x * u_xlat2.x;
    u_xlat2.x = u_xlat2.x * u_xlat11;
    u_xlat2.x = u_xlat2.x * u_xlat2.x;
    u_xlat2.x = u_xlat16_6.x * u_xlat2.x;
    u_xlat11 = (-u_xlat16_15.x) * u_xlat16_7.x + 0.0299999993;
    u_xlat2.x = u_xlat11 * u_xlat2.x;
    u_xlat0.x = u_xlat2.x * 0.180000007 + u_xlat0.y;
    u_xlat2.x = dot(vec3(1.45143926, -0.236510754, -0.214928567), u_xlat0.xzw);
    u_xlat2.y = dot(vec3(-0.0765537769, 1.17622972, -0.0996759236), u_xlat0.xzw);
    u_xlat2.z = dot(vec3(0.00831614807, -0.00603244966, 0.997716308), u_xlat0.xzw);
    u_xlat0.xyz = max(u_xlat2.xyz, vec3(0.0, 0.0, 0.0));
    u_xlat27 = dot(u_xlat0.xyz, vec3(0.272228986, 0.674081981, 0.0536894985));
    u_xlat0.xyz = (-vec3(u_xlat27)) + u_xlat0.xyz;
    u_xlat0.xyz = u_xlat0.xyz * vec3(0.959999979, 0.959999979, 0.959999979) + vec3(u_xlat27);
    u_xlat2.xyz = u_xlat0.xyz * vec3(2.78508496, 2.78508496, 2.78508496) + vec3(0.107772, 0.107772, 0.107772);
    u_xlat2.xyz = u_xlat0.xyz * u_xlat2.xyz;
    u_xlat3.xyz = u_xlat0.xyz * vec3(2.93604493, 2.93604493, 2.93604493) + vec3(0.887121975, 0.887121975, 0.887121975);
    u_xlat0.xyz = u_xlat0.xyz * u_xlat3.xyz + vec3(0.806888998, 0.806888998, 0.806888998);
    u_xlat0.xyz = u_xlat2.xyz / u_xlat0.xyz;
    u_xlat2.x = dot(vec3(0.662454188, 0.134004205, 0.156187683), u_xlat0.xyz);
    u_xlat2.y = dot(vec3(0.272228718, 0.674081743, 0.0536895171), u_xlat0.xyz);
    u_xlat2.z = dot(vec3(-0.00557464967, 0.0040607336, 1.01033914), u_xlat0.xyz);
    u_xlat16_6.x = dot(u_xlat2.xyz, vec3(1.0, 1.0, 1.0));
    u_xlat16_6.x = max(u_xlat16_6.x, 9.99999975e-05);
    u_xlat16_6.xy = u_xlat2.xy / u_xlat16_6.xx;
    u_xlat16_33 = max(u_xlat2.y, 0.0);
    u_xlat16_33 = min(u_xlat16_33, 65504.0);
    u_xlat16_33 = log2(u_xlat16_33);
    u_xlat16_33 = u_xlat16_33 * 0.981100023;
    u_xlat16_7.y = exp2(u_xlat16_33);
    u_xlat16_33 = max(u_xlat16_6.y, 9.99999975e-05);
    u_xlat16_33 = u_xlat16_7.y / u_xlat16_33;
    u_xlat16_34 = (-u_xlat16_6.x) + 1.0;
    u_xlat16_6.z = (-u_xlat16_6.y) + u_xlat16_34;
    u_xlat16_7.xz = vec2(u_xlat16_33) * u_xlat16_6.xz;
    u_xlat16_6.x = dot(vec3(1.6410234, -0.324803293, -0.236424699), u_xlat16_7.xyz);
    u_xlat16_6.y = dot(vec3(-0.663662851, 1.61533165, 0.0167563483), u_xlat16_7.xyz);
    u_xlat16_6.z = dot(vec3(0.0117218941, -0.00828444213, 0.988394856), u_xlat16_7.xyz);
    u_xlat0.x = dot(u_xlat16_6.xyz, vec3(0.272228986, 0.674081981, 0.0536894985));
    u_xlat9.xyz = (-u_xlat0.xxx) + u_xlat16_6.xyz;
    u_xlat0.xyz = u_xlat9.xyz * vec3(0.930000007, 0.930000007, 0.930000007) + u_xlat0.xxx;
    u_xlat2.x = dot(vec3(0.662454188, 0.134004205, 0.156187683), u_xlat0.xyz);
    u_xlat2.y = dot(vec3(0.272228718, 0.674081743, 0.0536895171), u_xlat0.xyz);
    u_xlat2.z = dot(vec3(-0.00557464967, 0.0040607336, 1.01033914), u_xlat0.xyz);
    u_xlat0.x = dot(vec3(0.987223983, -0.00611326983, 0.0159533005), u_xlat2.xyz);
    u_xlat0.y = dot(vec3(-0.00759836007, 1.00186002, 0.00533019984), u_xlat2.xyz);
    u_xlat0.z = dot(vec3(0.00307257008, -0.00509594986, 1.08168006), u_xlat2.xyz);
    u_xlat16_6.x = dot(vec3(3.2409699, -1.5373832, -0.498610765), u_xlat0.xyz);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_6.x = min(max(u_xlat16_6.x, 0.0), 1.0);
#else
    u_xlat16_6.x = clamp(u_xlat16_6.x, 0.0, 1.0);
#endif
    u_xlat16_6.y = dot(vec3(-0.969243646, 1.8759675, 0.0415550582), u_xlat0.xyz);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_6.y = min(max(u_xlat16_6.y, 0.0), 1.0);
#else
    u_xlat16_6.y = clamp(u_xlat16_6.y, 0.0, 1.0);
#endif
    u_xlat16_6.z = dot(vec3(0.0556300804, -0.203976959, 1.05697155), u_xlat0.xyz);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_6.z = min(max(u_xlat16_6.z, 0.0), 1.0);
#else
    u_xlat16_6.z = clamp(u_xlat16_6.z, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb0.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb0.x){
        u_xlat16_7.xyz = u_xlat16_6.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_8.xyz = log2(u_xlat16_6.xyz);
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_8.xyz = exp2(u_xlat16_8.xyz);
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_6.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_7;
            hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_7.x : u_xlat16_8.x;
            hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_7.y : u_xlat16_8.y;
            hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_7.z : u_xlat16_8.z;
            u_xlat16_7 = hlslcc_movcTemp;
        }
        u_xlat0.xyz = u_xlat16_7.zxy * _UserLut_Params.zzz;
        u_xlat0.x = floor(u_xlat0.x);
        u_xlat2.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat2.yz = u_xlat0.yz * _UserLut_Params.xy + u_xlat2.xy;
        u_xlat2.x = u_xlat0.x * _UserLut_Params.y + u_xlat2.y;
        u_xlat10_9.xyz = textureLod(_UserLut, u_xlat2.xz, 0.0).xyz;
        u_xlat3.x = _UserLut_Params.y;
        u_xlat3.y = 0.0;
        u_xlat2.xy = u_xlat2.xz + u_xlat3.xy;
        u_xlat10_2.xyz = textureLod(_UserLut, u_xlat2.xy, 0.0).xyz;
        u_xlat0.x = u_xlat16_7.z * _UserLut_Params.z + (-u_xlat0.x);
        u_xlat2.xyz = (-u_xlat10_9.xyz) + u_xlat10_2.xyz;
        u_xlat0.xyz = u_xlat0.xxx * u_xlat2.xyz + u_xlat10_9.xyz;
        u_xlat0.xyz = (-u_xlat16_7.xyz) + u_xlat0.xyz;
        u_xlat0.xyz = _UserLut_Params.www * u_xlat0.xyz + u_xlat16_7.xyz;
        u_xlat16_7.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_8.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_8.xyz = log2(abs(u_xlat16_8.xyz));
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_8.xyz = exp2(u_xlat16_8.xyz);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
        u_xlat16_6.x = (u_xlatb0.x) ? u_xlat16_7.x : u_xlat16_8.x;
        u_xlat16_6.y = (u_xlatb0.y) ? u_xlat16_7.y : u_xlat16_8.y;
        u_xlat16_6.z = (u_xlatb0.z) ? u_xlat16_7.z : u_xlat16_8.z;
    }
    u_xlat0.xyz = u_xlat16_6.zxy * _Lut_Params.zzz;
    u_xlat0.x = floor(u_xlat0.x);
    u_xlat2.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat2.yz = u_xlat0.yz * _Lut_Params.xy + u_xlat2.xy;
    u_xlat2.x = u_xlat0.x * _Lut_Params.y + u_xlat2.y;
    u_xlat10_9.xyz = textureLod(_InternalLut, u_xlat2.xz, 0.0).xyz;
    u_xlat3.x = _Lut_Params.y;
    u_xlat3.y = 0.0;
    u_xlat2.xy = u_xlat2.xz + u_xlat3.xy;
    u_xlat10_2.xyz = textureLod(_InternalLut, u_xlat2.xy, 0.0).xyz;
    u_xlat0.x = u_xlat16_6.z * _Lut_Params.z + (-u_xlat0.x);
    u_xlat2.xyz = (-u_xlat10_9.xyz) + u_xlat10_2.xyz;
    u_xlat0.xyz = u_xlat0.xxx * u_xlat2.xyz + u_xlat10_9.xyz;
    SV_Target0.xyz = u_xlat0.xyz;
    SV_Target0.w = 1.0;
    return;
}

#endif
                                $Globalsp         _Lut_Params                          _UserLut_Params                         _Distortion_Params1                          _Distortion_Params2                   0      _Chroma_Params                    @      _Vignette_Params1                     P      _Vignette_Params2                     `          $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      _InternalLut                _UserLut                UnityStereoEyeIndices                  