@'  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL      _FXAA      _LINEAR_TO_SRGB_CONVERSION  {%  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceSize;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec4 u_xlat16_0;
uvec4 u_xlatu0;
vec4 u_xlat1;
mediump vec3 u_xlat10_1;
ivec4 u_xlati1;
bvec3 u_xlatb1;
vec4 u_xlat2;
mediump vec4 u_xlat10_2;
ivec4 u_xlati2;
uvec4 u_xlatu2;
vec4 u_xlat3;
uvec4 u_xlatu4;
vec3 u_xlat5;
mediump vec3 u_xlat16_6;
mediump vec3 u_xlat16_7;
mediump vec3 u_xlat16_8;
mediump vec3 u_xlat16_9;
bool u_xlatb11;
mediump vec3 u_xlat16_16;
mediump vec3 u_xlat16_17;
mediump float u_xlat16_26;
mediump float u_xlat16_27;
mediump float u_xlat16_36;
mediump float u_xlat16_38;
void main()
{
    u_xlat0.xy = vs_TEXCOORD0.xy * _SourceSize.xy;
    u_xlati1 = ivec4(u_xlat0.xyxy);
    u_xlat0.xy = trunc(u_xlat0.xy);
    u_xlat0.xy = max(u_xlat0.xy, vec2(0.0, 0.0));
    u_xlati2 = u_xlati1.zwzw + ivec4(int(0xFFFFFFFFu), int(0xFFFFFFFFu), 1, int(0xFFFFFFFFu));
    u_xlati1 = u_xlati1 + ivec4(int(0xFFFFFFFFu), 1, 1, 1);
    u_xlat1 = vec4(u_xlati1);
    u_xlat1 = max(u_xlat1, vec4(0.0, 0.0, 0.0, 0.0));
    u_xlat2 = vec4(u_xlati2);
    u_xlat2 = max(u_xlat2, vec4(0.0, 0.0, 0.0, 0.0));
    u_xlat3 = _SourceSize.xyxy + vec4(-1.0, -1.0, -1.0, -1.0);
    u_xlat2 = min(u_xlat2, u_xlat3);
    u_xlatu2 =  uvec4(ivec4(u_xlat2));
    u_xlatu4.xy = u_xlatu2.zw;
    u_xlatu2.w = uint(0u);
    u_xlatu2.z = uint(vs_BLENDWEIGHT0);
    u_xlatu4.zw = u_xlatu2.zw;
    u_xlat5.xyz = texelFetch(_SourceTex, ivec3(u_xlatu2.xyz), int(u_xlatu2.w)).xyz;
    u_xlat16_6.xyz = u_xlat5.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_6.xyz = min(max(u_xlat16_6.xyz, 0.0), 1.0);
#else
    u_xlat16_6.xyz = clamp(u_xlat16_6.xyz, 0.0, 1.0);
#endif
    u_xlat16_6.x = dot(u_xlat16_6.xyz, vec3(0.212672904, 0.715152204, 0.0721750036));
    u_xlat5.xyz = texelFetch(_SourceTex, ivec3(u_xlatu4.xyz), int(u_xlatu4.w)).xyz;
    u_xlat16_16.xyz = u_xlat5.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_16.xyz = min(max(u_xlat16_16.xyz, 0.0), 1.0);
#else
    u_xlat16_16.xyz = clamp(u_xlat16_16.xyz, 0.0, 1.0);
#endif
    u_xlat16_16.x = dot(u_xlat16_16.xyz, vec3(0.212672904, 0.715152204, 0.0721750036));
    u_xlat1 = min(u_xlat1, u_xlat3);
    u_xlat0.xy = min(u_xlat0.xy, u_xlat3.xy);
    u_xlatu2.xy =  uvec2(ivec2(u_xlat0.xy));
    u_xlat0.xyz = texelFetch(_SourceTex, ivec3(u_xlatu2.xyz), int(u_xlatu2.w)).xyz;
    u_xlat2.z = float(u_xlatu2.z);
    u_xlat16_7.xyz = u_xlat0.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_7.xyz = min(max(u_xlat16_7.xyz, 0.0), 1.0);
#else
    u_xlat16_7.xyz = clamp(u_xlat16_7.xyz, 0.0, 1.0);
#endif
    u_xlat16_26 = dot(u_xlat16_7.xyz, vec3(0.212672904, 0.715152204, 0.0721750036));
    u_xlatu0 =  uvec4(ivec4(u_xlat1.zwxy));
    u_xlatu4.xy = u_xlatu0.zw;
    u_xlat1.xyz = texelFetch(_SourceTex, ivec3(u_xlatu4.xyz), int(u_xlatu4.w)).xyz;
    u_xlatu0.zw = u_xlatu4.zw;
    u_xlat0.xyz = texelFetch(_SourceTex, ivec3(u_xlatu0.xyz), int(u_xlatu0.w)).xyz;
    u_xlat16_7.xyz = u_xlat0.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_7.xyz = min(max(u_xlat16_7.xyz, 0.0), 1.0);
#else
    u_xlat16_7.xyz = clamp(u_xlat16_7.xyz, 0.0, 1.0);
#endif
    u_xlat16_36 = dot(u_xlat16_7.xyz, vec3(0.212672904, 0.715152204, 0.0721750036));
    u_xlat16_7.xyz = u_xlat1.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_7.xyz = min(max(u_xlat16_7.xyz, 0.0), 1.0);
#else
    u_xlat16_7.xyz = clamp(u_xlat16_7.xyz, 0.0, 1.0);
#endif
    u_xlat16_7.x = dot(u_xlat16_7.xyz, vec3(0.212672904, 0.715152204, 0.0721750036));
    u_xlat16_17.x = u_xlat16_36 + u_xlat16_16.x;
    u_xlat16_27 = u_xlat16_6.x + u_xlat16_7.x;
    u_xlat16_0.yw = (-u_xlat16_17.xx) + vec2(u_xlat16_27);
    u_xlat16_17.x = u_xlat16_16.x + u_xlat16_6.x;
    u_xlat16_27 = u_xlat16_36 + u_xlat16_7.x;
    u_xlat16_27 = (-u_xlat16_27) + u_xlat16_17.x;
    u_xlat16_17.x = u_xlat16_7.x + u_xlat16_17.x;
    u_xlat16_17.x = u_xlat16_36 + u_xlat16_17.x;
    u_xlat16_17.x = u_xlat16_17.x * 0.03125;
    u_xlat16_17.x = max(u_xlat16_17.x, 0.0078125);
    u_xlat1.x = min(abs(u_xlat16_0.w), abs(u_xlat16_27));
    u_xlat16_0.xz = (-vec2(u_xlat16_27));
    u_xlat1.x = u_xlat16_17.x + u_xlat1.x;
    u_xlat1.x = float(1.0) / float(u_xlat1.x);
    u_xlat0 = u_xlat16_0 * u_xlat1.xxxx;
    u_xlat0 = max(u_xlat0, vec4(-8.0, -8.0, -8.0, -8.0));
    u_xlat0 = min(u_xlat0, vec4(8.0, 8.0, 8.0, 8.0));
    u_xlat0 = u_xlat0 * _SourceSize.zwzw;
    u_xlat1 = u_xlat0.zwzw * vec4(-0.166666672, -0.166666672, -0.5, -0.5) + vs_TEXCOORD0.xyxy;
    u_xlat0 = u_xlat0 * vec4(0.166666672, 0.166666672, 0.5, 0.5) + vs_TEXCOORD0.xyxy;
    u_xlat2.xy = u_xlat1.zw;
    u_xlat10_2.xyw = texture(_SourceTex, u_xlat2.xyz).xyz;
    u_xlat1.z = u_xlat2.z;
    u_xlat16_17.xyz = u_xlat10_2.xyw;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_17.xyz = min(max(u_xlat16_17.xyz, 0.0), 1.0);
#else
    u_xlat16_17.xyz = clamp(u_xlat16_17.xyz, 0.0, 1.0);
#endif
    u_xlat2.xy = u_xlat0.zw;
    u_xlat0.z = u_xlat1.z;
    u_xlat10_1.xyz = texture(_SourceTex, u_xlat1.xyz).xyz;
    u_xlat16_8.xyz = u_xlat10_1.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_8.xyz = min(max(u_xlat16_8.xyz, 0.0), 1.0);
#else
    u_xlat16_8.xyz = clamp(u_xlat16_8.xyz, 0.0, 1.0);
#endif
    u_xlat2.z = u_xlat0.z;
    u_xlat10_1.xyz = texture(_SourceTex, u_xlat0.xyz).xyz;
    u_xlat16_9.xyz = u_xlat10_1.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_9.xyz = min(max(u_xlat16_9.xyz, 0.0), 1.0);
#else
    u_xlat16_9.xyz = clamp(u_xlat16_9.xyz, 0.0, 1.0);
#endif
    u_xlat16_8.xyz = u_xlat16_8.xyz + u_xlat16_9.xyz;
    u_xlat10_1.xyz = texture(_SourceTex, u_xlat2.xyz).xyz;
    u_xlat16_9.xyz = u_xlat10_1.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_9.xyz = min(max(u_xlat16_9.xyz, 0.0), 1.0);
#else
    u_xlat16_9.xyz = clamp(u_xlat16_9.xyz, 0.0, 1.0);
#endif
    u_xlat16_17.xyz = u_xlat16_17.xyz + u_xlat16_9.xyz;
    u_xlat16_17.xyz = u_xlat16_17.xyz * vec3(0.25, 0.25, 0.25);
    u_xlat16_17.xyz = u_xlat16_8.xyz * vec3(0.25, 0.25, 0.25) + u_xlat16_17.xyz;
    u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(0.5, 0.5, 0.5);
    u_xlat16_38 = dot(u_xlat16_17.xyz, vec3(0.212672904, 0.715152204, 0.0721750036));
    u_xlat16_9.x = min(u_xlat16_16.x, u_xlat16_7.x);
    u_xlat16_16.x = max(u_xlat16_16.x, u_xlat16_7.x);
    u_xlat16_16.x = max(u_xlat16_36, u_xlat16_16.x);
    u_xlat16_36 = min(u_xlat16_36, u_xlat16_9.x);
    u_xlat16_7.x = min(u_xlat16_6.x, u_xlat16_26);
    u_xlat16_6.x = max(u_xlat16_6.x, u_xlat16_26);
    u_xlat16_6.x = max(u_xlat16_16.x, u_xlat16_6.x);
#ifdef UNITY_ADRENO_ES3
    u_xlatb1.x = !!(u_xlat16_6.x<u_xlat16_38);
#else
    u_xlatb1.x = u_xlat16_6.x<u_xlat16_38;
#endif
    u_xlat16_6.x = min(u_xlat16_36, u_xlat16_7.x);
#ifdef UNITY_ADRENO_ES3
    u_xlatb11 = !!(u_xlat16_38<u_xlat16_6.x);
#else
    u_xlatb11 = u_xlat16_38<u_xlat16_6.x;
#endif
    u_xlatb1.x = u_xlatb1.x || u_xlatb11;
    u_xlat16_6.xyz = (u_xlatb1.x) ? u_xlat16_8.xyz : u_xlat16_17.xyz;
    u_xlat16_7.xyz = log2(u_xlat16_6.xyz);
    u_xlat16_7.xyz = u_xlat16_7.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_7.xyz = exp2(u_xlat16_7.xyz);
    u_xlat16_7.xyz = u_xlat16_7.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlat16_8.xyz = u_xlat16_6.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlatb1.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_6.xyzx).xyz;
    SV_Target0.x = (u_xlatb1.x) ? u_xlat16_8.x : u_xlat16_7.x;
    SV_Target0.y = (u_xlatb1.y) ? u_xlat16_8.y : u_xlat16_7.y;
    SV_Target0.z = (u_xlatb1.z) ? u_xlat16_8.z : u_xlat16_7.z;
    SV_Target0.w = 1.0;
    return;
}

#endif
                               $Globals         _SourceSize                              $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      UnityStereoEyeIndices                  