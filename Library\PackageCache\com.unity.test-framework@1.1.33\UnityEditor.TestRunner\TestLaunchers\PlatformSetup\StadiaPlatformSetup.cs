namespace UnityEditor.TestTools.TestRunner
{
    internal class StadiaPlatformSetup : IPlatformSetup
    {
        public void Setup()
        {
        }

        public void PostBuildAction()
        {
        }

        public void PostSuccessfulBuildAction()
        {
        }

        public void PostSuccessfulLaunchAction()
        {
        }

        public void CleanUp()
        {
        }
    }
}
