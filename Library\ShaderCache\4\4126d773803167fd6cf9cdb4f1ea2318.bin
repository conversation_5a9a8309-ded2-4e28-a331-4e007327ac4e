  <Q                         STEREO_MULTIVIEW_ON       _HIGH_QUALITY_SAMPLING  M  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 hlslcc_mtx4x4_FullscreenProjMat[4];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
in highp vec4 in_POSITION0;
in highp vec2 in_TEXCOORD0;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
vec4 u_xlat0;
uint u_xlatu0;
void main()
{
    u_xlat0 = in_POSITION0.yyyy * hlslcc_mtx4x4_FullscreenProjMat[1];
    u_xlat0 = hlslcc_mtx4x4_FullscreenProjMat[0] * in_POSITION0.xxxx + u_xlat0;
    u_xlat0 = hlslcc_mtx4x4_FullscreenProjMat[2] * in_POSITION0.zzzz + u_xlat0;
    gl_Position = u_xlat0 + hlslcc_mtx4x4_FullscreenProjMat[3];
    u_xlatu0 = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0)].x;
    vs_TEXCOORD0.xy = in_TEXCOORD0.xy;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceSize;
UNITY_LOCATION(0) uniform mediump sampler2DArray _ColorTexture;
UNITY_LOCATION(1) uniform mediump sampler2DArray _FullCoCTexture;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump float SV_Target0;
layout(location = 1) out mediump vec3 SV_Target1;
vec4 u_xlat0;
mediump vec3 u_xlat10_0;
uint u_xlatu0;
mediump vec3 u_xlat10_1;
vec4 u_xlat2;
mediump vec3 u_xlat10_3;
mediump vec3 u_xlat16_4;
mediump vec3 u_xlat10_5;
mediump float u_xlat10_15;
mediump float u_xlat10_16;
mediump float u_xlat16_19;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat0.z = float(u_xlatu0);
    u_xlat0.xy = vs_TEXCOORD0.xy;
    u_xlat10_15 = texture(_FullCoCTexture, u_xlat0.xyz).x;
    u_xlat10_1.xyz = texture(_ColorTexture, u_xlat0.xyz).xyz;
    u_xlat2 = _SourceSize.zwzw * vec4(-0.899999976, 0.400000006, 0.899999976, -0.400000006) + vs_TEXCOORD0.xyxy;
    u_xlat0.xy = u_xlat2.zw;
    u_xlat10_16 = texture(_FullCoCTexture, u_xlat0.xyz).x;
    u_xlat10_3.xyz = texture(_ColorTexture, u_xlat0.xyz).xyz;
    u_xlat2.z = u_xlat0.z;
    u_xlat16_4.xyz = vec3(u_xlat10_16) * u_xlat10_3.xyz;
    u_xlat16_19 = u_xlat10_15 + u_xlat10_16;
    u_xlat16_4.xyz = u_xlat10_1.xyz * vec3(u_xlat10_15) + u_xlat16_4.xyz;
    u_xlat10_0.x = texture(_FullCoCTexture, u_xlat2.xyz).x;
    u_xlat10_5.xyz = texture(_ColorTexture, u_xlat2.xyz).xyz;
    u_xlat16_4.xyz = u_xlat10_5.xyz * u_xlat10_0.xxx + u_xlat16_4.xyz;
    u_xlat16_19 = u_xlat10_0.x + u_xlat16_19;
    u_xlat0 = _SourceSize.zwzw * vec4(-0.400000006, -0.899999976, 0.400000006, 0.899999976) + vs_TEXCOORD0.xyxy;
    u_xlat2.xy = u_xlat0.zw;
    u_xlat10_15 = texture(_FullCoCTexture, u_xlat2.xyz).x;
    u_xlat10_1.xyz = texture(_ColorTexture, u_xlat2.xyz).xyz;
    u_xlat0.z = u_xlat2.z;
    u_xlat16_4.xyz = u_xlat10_1.xyz * vec3(u_xlat10_15) + u_xlat16_4.xyz;
    u_xlat16_19 = u_xlat10_15 + u_xlat16_19;
    u_xlat10_15 = texture(_FullCoCTexture, u_xlat0.xyz).x;
    u_xlat10_0.xyz = texture(_ColorTexture, u_xlat0.xyz).xyz;
    u_xlat16_4.xyz = u_xlat10_0.xyz * vec3(u_xlat10_15) + u_xlat16_4.xyz;
    u_xlat16_19 = u_xlat10_15 + u_xlat16_19;
    u_xlat0.x = u_xlat16_19 * 0.200000003;
    SV_Target0 = u_xlat0.x;
    u_xlat0.xyz = u_xlat16_4.xyz * vec3(0.200000003, 0.200000003, 0.200000003);
    SV_Target1.xyz = u_xlat0.xyz;
    return;
}

#endif
                                $Globals         _SourceSize                              $Globals@         _FullscreenProjMat                              UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _ColorTexture               
      _FullCoCTexture           
      UnityStereoEyeIndices                  