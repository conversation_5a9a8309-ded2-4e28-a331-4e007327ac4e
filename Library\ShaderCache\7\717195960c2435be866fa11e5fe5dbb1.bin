4  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL    g  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceTex_TexelSize;
uniform 	float _SampleOffset;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec4 u_xlat16_0;
uint u_xlatu0;
vec4 u_xlat1;
mediump vec4 u_xlat16_1;
vec4 u_xlat2;
mediump vec4 u_xlat16_3;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat0.z = float(u_xlatu0);
    u_xlat1 = vec4(_SampleOffset) * vec4(-1.0, -1.0, 1.0, 1.0);
    u_xlat2 = _SourceTex_TexelSize.xyxy * u_xlat1.zyxy + vs_TEXCOORD0.xyxy;
    u_xlat1 = _SourceTex_TexelSize.xyxy * u_xlat1.zwxw + vs_TEXCOORD0.xyxy;
    u_xlat0.xy = u_xlat2.zw;
    u_xlat16_3 = texture(_SourceTex, u_xlat0.xyz);
    u_xlat2.z = u_xlat0.z;
    u_xlat16_0 = texture(_SourceTex, u_xlat2.xyz);
    u_xlat0 = u_xlat16_0 + u_xlat16_3;
    u_xlat2.xy = u_xlat1.zw;
    u_xlat16_3 = texture(_SourceTex, u_xlat2.xyz);
    u_xlat1.z = u_xlat2.z;
    u_xlat16_1 = texture(_SourceTex, u_xlat1.xyz);
    u_xlat0 = u_xlat0 + u_xlat16_3;
    u_xlat0 = u_xlat16_1 + u_xlat0;
    u_xlat0 = u_xlat0 * vec4(0.25, 0.25, 0.25, 0.25);
    SV_Target0 = u_xlat0;
    return;
}

#endif
                               $Globals         _SourceTex_TexelSize                      
   _SampleOffset                               $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      UnityStereoEyeIndices                  