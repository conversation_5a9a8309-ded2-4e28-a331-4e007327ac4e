\
  <Q                         _USE_DRAW_PROCEDURAL      #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
out highp vec2 vs_TEXCOORD0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceSize;
UNITY_LOCATION(0) uniform mediump sampler2D _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _ColorTexture;
UNITY_LOCATION(2) uniform mediump sampler2D _FullCoCTexture;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec2 u_xlat0;
uvec4 u_xlatu0;
vec3 u_xlat1;
mediump float u_xlat16_2;
mediump vec3 u_xlat10_3;
bool u_xlatb3;
mediump vec3 u_xlat16_5;
void main()
{
    u_xlat0.xy = vs_TEXCOORD0.xy * _SourceSize.xy;
    u_xlatu0.xy =  uvec2(ivec2(u_xlat0.xy));
    u_xlatu0.z = uint(uint(0u));
    u_xlatu0.w = uint(uint(0u));
    u_xlat1.xyz = texelFetch(_SourceTex, ivec2(u_xlatu0.xy), int(u_xlatu0.w)).xyz;
    u_xlat0.x = texelFetch(_FullCoCTexture, ivec2(u_xlatu0.xy), int(u_xlatu0.w)).x;
#ifdef UNITY_ADRENO_ES3
    u_xlatb3 = !!(0.0<u_xlat0.x);
#else
    u_xlatb3 = 0.0<u_xlat0.x;
#endif
    if(u_xlatb3){
        u_xlat10_3.xyz = texture(_ColorTexture, vs_TEXCOORD0.xy).xyz;
        u_xlat16_2 = u_xlat0.x * 6.28318548;
        u_xlat16_2 = sqrt(u_xlat16_2);
        u_xlat16_5.x = min(u_xlat16_2, 1.0);
        u_xlat16_5.xyz = u_xlat10_3.xyz * u_xlat16_5.xxx;
        u_xlat16_2 = (-u_xlat16_2) + 1.0;
        u_xlat16_2 = max(u_xlat16_2, 0.0);
    } else {
        u_xlat16_2 = float(1.0);
        u_xlat16_5.x = float(0.0);
        u_xlat16_5.y = float(0.0);
        u_xlat16_5.z = float(0.0);
    }
    SV_Target0.xyz = u_xlat1.xyz * vec3(u_xlat16_2) + u_xlat16_5.xyz;
    SV_Target0.w = 1.0;
    return;
}

#endif
                                 $Globals         _SourceSize                              $Globals      
   _ScaleBias                               
   _SourceTex                 
   _ColorTexture                   _FullCoCTexture                  