,-  <Q                         _USE_DRAW_PROCEDURAL   	   _BLOOM_HQ      _CHROMATIC_ABERRATION      _LINEAR_TO_SRGB_CONVERSION  A*  #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
out highp vec2 vs_TEXCOORD0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	vec4 _Bloom_Params;
uniform 	float _Bloom_RGBM;
uniform 	float _Chroma_Params;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
uniform 	vec4 _Bloom_Texture_TexelSize;
UNITY_LOCATION(0) uniform mediump sampler2D _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _Bloom_Texture;
UNITY_LOCATION(2) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(3) uniform mediump sampler2D _UserLut;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec4 u_xlat10_0;
bvec3 u_xlatb0;
vec3 u_xlat1;
mediump vec3 u_xlat16_1;
mediump vec3 u_xlat10_1;
vec4 u_xlat2;
mediump vec4 u_xlat16_2;
mediump vec4 u_xlat10_2;
vec4 u_xlat3;
mediump vec2 u_xlat16_3;
mediump vec4 u_xlat10_3;
vec3 u_xlat4;
mediump vec3 u_xlat16_4;
vec3 u_xlat5;
mediump vec4 u_xlat10_5;
mediump vec3 u_xlat16_6;
mediump vec3 u_xlat16_7;
mediump vec3 u_xlat16_8;
vec2 u_xlat9;
mediump vec3 u_xlat10_10;
vec2 u_xlat20;
vec2 u_xlat24;
float u_xlat30;
bool u_xlatb30;
bool u_xlatb31;
void main()
{
    u_xlat0 = vs_TEXCOORD0.xyxy * vec4(2.0, 2.0, 2.0, 2.0) + vec4(-1.0, -1.0, -1.0, -1.0);
    u_xlat1.x = dot(u_xlat0.zw, u_xlat0.zw);
    u_xlat0 = u_xlat0 * u_xlat1.xxxx;
    u_xlat0 = u_xlat0 * vec4(_Chroma_Params);
    u_xlat10_1.x = texture(_SourceTex, vs_TEXCOORD0.xy).x;
    u_xlat0 = u_xlat0 * vec4(-0.333333343, -0.333333343, -0.666666687, -0.666666687) + vs_TEXCOORD0.xyxy;
    u_xlat10_1.y = texture(_SourceTex, u_xlat0.xy).y;
    u_xlat10_1.z = texture(_SourceTex, u_xlat0.zw).z;
    u_xlat0.xy = vs_TEXCOORD0.xy * _Bloom_Texture_TexelSize.zw + vec2(0.5, 0.5);
    u_xlat20.xy = floor(u_xlat0.xy);
    u_xlat0.xy = fract(u_xlat0.xy);
    u_xlat16_2 = (-u_xlat0.xyxy) * vec4(0.5, 0.5, 0.166666672, 0.166666672) + vec4(0.5, 0.5, 0.5, 0.5);
    u_xlat16_2 = u_xlat0.xyxy * u_xlat16_2 + vec4(0.5, 0.5, -0.5, -0.5);
    u_xlat16_3.xy = u_xlat0.xy * vec2(0.5, 0.5) + vec2(-1.0, -1.0);
    u_xlat16_3.xy = u_xlat0.xy * u_xlat16_3.xy;
    u_xlat16_3.xy = u_xlat16_3.xy * u_xlat0.xy + vec2(0.666666687, 0.666666687);
    u_xlat16_2 = u_xlat0.xyxy * u_xlat16_2 + vec4(0.166666672, 0.166666672, 0.166666672, 0.166666672);
    u_xlat0.xy = (-u_xlat16_3.xy) + vec2(1.0, 1.0);
    u_xlat0.xy = (-u_xlat16_2.xy) + u_xlat0.xy;
    u_xlat0.xy = (-u_xlat16_2.zw) + u_xlat0.xy;
    u_xlat4.xy = u_xlat16_3.xy + u_xlat16_2.zw;
    u_xlat24.xy = u_xlat0.xy + u_xlat16_2.xy;
    u_xlat5.xy = vec2(1.0) / vec2(u_xlat4.xy);
    u_xlat2.zw = u_xlat16_3.xy * u_xlat5.xy + vec2(-1.0, -1.0);
    u_xlat5.xy = vec2(1.0) / vec2(u_xlat24.xy);
    u_xlat2.xy = u_xlat0.xy * u_xlat5.xy + vec2(1.0, 1.0);
    u_xlat3 = u_xlat20.xyxy + u_xlat2.zwxw;
    u_xlat3 = u_xlat3 + vec4(-0.5, -0.5, -0.5, -0.5);
    u_xlat3 = u_xlat3 * _Bloom_Texture_TexelSize.xyxy;
    u_xlat3 = min(u_xlat3, vec4(1.0, 1.0, 1.0, 1.0));
    u_xlat10_5 = textureLod(_Bloom_Texture, u_xlat3.xy, 0.0);
    u_xlat10_3 = textureLod(_Bloom_Texture, u_xlat3.zw, 0.0);
    u_xlat3 = u_xlat10_3 * u_xlat24.xxxx;
    u_xlat3 = u_xlat4.xxxx * u_xlat10_5 + u_xlat3;
    u_xlat0 = u_xlat20.xyxy + u_xlat2.zyxy;
    u_xlat0 = u_xlat0 + vec4(-0.5, -0.5, -0.5, -0.5);
    u_xlat0 = u_xlat0 * _Bloom_Texture_TexelSize.xyxy;
    u_xlat0 = min(u_xlat0, vec4(1.0, 1.0, 1.0, 1.0));
    u_xlat10_2 = textureLod(_Bloom_Texture, u_xlat0.xy, 0.0);
    u_xlat10_0 = textureLod(_Bloom_Texture, u_xlat0.zw, 0.0);
    u_xlat0 = u_xlat10_0 * u_xlat24.xxxx;
    u_xlat0 = u_xlat4.xxxx * u_xlat10_2 + u_xlat0;
    u_xlat0 = u_xlat0 * u_xlat24.yyyy;
    u_xlat0 = u_xlat4.yyyy * u_xlat3 + u_xlat0;
#ifdef UNITY_ADRENO_ES3
    u_xlatb31 = !!(0.0<_Bloom_RGBM);
#else
    u_xlatb31 = 0.0<_Bloom_RGBM;
#endif
    if(u_xlatb31){
        u_xlat16_6.xyz = u_xlat0.www * u_xlat0.xyz;
        u_xlat4.xyz = u_xlat16_6.xyz * vec3(8.0, 8.0, 8.0);
        u_xlat16_4.xyz = u_xlat4.xyz;
    } else {
        u_xlat16_4.xyz = u_xlat0.xyz;
    }
    u_xlat0.xyz = u_xlat16_4.xyz * _Bloom_Params.xxx;
    u_xlat0.xyz = u_xlat0.xyz * _Bloom_Params.yzw + u_xlat10_1.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlatb30 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb30 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb30){
        u_xlat1.xy = vs_TEXCOORD0.xy + (-_Vignette_Params2.xy);
        u_xlat1.yz = abs(u_xlat1.xy) * _Vignette_Params2.zz;
        u_xlat1.x = u_xlat1.y * _Vignette_Params1.w;
        u_xlat30 = dot(u_xlat1.xz, u_xlat1.xz);
        u_xlat30 = (-u_xlat30) + 1.0;
        u_xlat30 = max(u_xlat30, 0.0);
        u_xlat30 = log2(u_xlat30);
        u_xlat30 = u_xlat30 * _Vignette_Params2.w;
        u_xlat30 = exp2(u_xlat30);
        u_xlat1.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat1.xyz = vec3(u_xlat30) * u_xlat1.xyz + _Vignette_Params1.xyz;
        u_xlat1.xyz = u_xlat0.xyz * u_xlat1.xyz;
        u_xlat16_1.xyz = u_xlat1.xyz;
    } else {
        u_xlat16_1.xyz = u_xlat0.xyz;
    }
    u_xlat16_6.xyz = u_xlat16_1.xyz * _Lut_Params.www;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_6.xyz = min(max(u_xlat16_6.xyz, 0.0), 1.0);
#else
    u_xlat16_6.xyz = clamp(u_xlat16_6.xyz, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb0.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb0.x){
        u_xlat16_7.xyz = u_xlat16_6.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_8.xyz = log2(u_xlat16_6.xyz);
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_8.xyz = exp2(u_xlat16_8.xyz);
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_6.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_7;
            hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_7.x : u_xlat16_8.x;
            hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_7.y : u_xlat16_8.y;
            hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_7.z : u_xlat16_8.z;
            u_xlat16_7 = hlslcc_movcTemp;
        }
        u_xlat0.xyz = u_xlat16_7.zxy * _UserLut_Params.zzz;
        u_xlat0.x = floor(u_xlat0.x);
        u_xlat5.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat5.yz = u_xlat0.yz * _UserLut_Params.xy + u_xlat5.xy;
        u_xlat5.x = u_xlat0.x * _UserLut_Params.y + u_xlat5.y;
        u_xlat10_10.xyz = textureLod(_UserLut, u_xlat5.xz, 0.0).xyz;
        u_xlat9.x = _UserLut_Params.y;
        u_xlat9.y = 0.0;
        u_xlat5.xy = u_xlat5.xz + u_xlat9.xy;
        u_xlat10_5.xyz = textureLod(_UserLut, u_xlat5.xy, 0.0).xyz;
        u_xlat0.x = u_xlat16_7.z * _UserLut_Params.z + (-u_xlat0.x);
        u_xlat5.xyz = (-u_xlat10_10.xyz) + u_xlat10_5.xyz;
        u_xlat0.xyz = u_xlat0.xxx * u_xlat5.xyz + u_xlat10_10.xyz;
        u_xlat0.xyz = (-u_xlat16_7.xyz) + u_xlat0.xyz;
        u_xlat0.xyz = _UserLut_Params.www * u_xlat0.xyz + u_xlat16_7.xyz;
        u_xlat16_7.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_8.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_8.xyz = log2(abs(u_xlat16_8.xyz));
        u_xlat16_8.xyz = u_xlat16_8.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_8.xyz = exp2(u_xlat16_8.xyz);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
        u_xlat16_6.x = (u_xlatb0.x) ? u_xlat16_7.x : u_xlat16_8.x;
        u_xlat16_6.y = (u_xlatb0.y) ? u_xlat16_7.y : u_xlat16_8.y;
        u_xlat16_6.z = (u_xlatb0.z) ? u_xlat16_7.z : u_xlat16_8.z;
    }
    u_xlat0.xyz = u_xlat16_6.zxy * _Lut_Params.zzz;
    u_xlat0.x = floor(u_xlat0.x);
    u_xlat5.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat5.yz = u_xlat0.yz * _Lut_Params.xy + u_xlat5.xy;
    u_xlat5.x = u_xlat0.x * _Lut_Params.y + u_xlat5.y;
    u_xlat10_10.xyz = textureLod(_InternalLut, u_xlat5.xz, 0.0).xyz;
    u_xlat9.x = _Lut_Params.y;
    u_xlat9.y = 0.0;
    u_xlat5.xy = u_xlat5.xz + u_xlat9.xy;
    u_xlat10_5.xyz = textureLod(_InternalLut, u_xlat5.xy, 0.0).xyz;
    u_xlat0.x = u_xlat16_6.z * _Lut_Params.z + (-u_xlat0.x);
    u_xlat5.xyz = (-u_xlat10_10.xyz) + u_xlat10_5.xyz;
    u_xlat0.xyz = u_xlat0.xxx * u_xlat5.xyz + u_xlat10_10.xyz;
    u_xlat16_6.xyz = u_xlat0.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlat16_7.xyz = log2(abs(u_xlat0.xyz));
    u_xlat16_7.xyz = u_xlat16_7.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_7.xyz = exp2(u_xlat16_7.xyz);
    u_xlat16_7.xyz = u_xlat16_7.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat0.xyzx).xyz;
    SV_Target0.x = (u_xlatb0.x) ? u_xlat16_6.x : u_xlat16_7.x;
    SV_Target0.y = (u_xlatb0.y) ? u_xlat16_6.y : u_xlat16_7.y;
    SV_Target0.z = (u_xlatb0.z) ? u_xlat16_6.z : u_xlat16_7.z;
    SV_Target0.w = 1.0;
    return;
}

#endif
                                 $Globalsp         _Lut_Params                          _UserLut_Params                      
   _Bloom_Params                            _Bloom_RGBM                   0      _Chroma_Params                    4      _Vignette_Params1                     @      _Vignette_Params2                     P      _Bloom_Texture_TexelSize                  `          $Globals      
   _ScaleBias                               
   _SourceTex                    _Bloom_Texture                  _InternalLut                _UserLut                 