t  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL    \  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceSize;
uniform 	vec4 _DownSampleScaleFactor;
uniform 	vec3 _CoCParams;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2DArray _HalfCoCTexture;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec3 u_xlat0;
mediump vec4 u_xlat16_0;
vec3 u_xlat1;
mediump vec4 u_xlat16_1;
uvec4 u_xlatu1;
vec4 u_xlat2;
mediump vec4 u_xlat16_3;
mediump vec4 u_xlat10_3;
mediump vec2 u_xlat16_4;
mediump vec4 u_xlat10_5;
vec2 u_xlat6;
void main()
{
    u_xlat0.xyz = _SourceSize.zxy * _DownSampleScaleFactor.zxy;
    u_xlat6.xy = u_xlat0.yz * vs_TEXCOORD0.xy;
    u_xlatu1.xy =  uvec2(ivec2(u_xlat6.xy));
    u_xlatu1.w = uint(0u);
    u_xlatu1.z = uint(vs_BLENDWEIGHT0);
    u_xlat6.x = texelFetch(_HalfCoCTexture, ivec3(u_xlatu1.xyz), int(u_xlatu1.w)).x;
    u_xlat1.z = float(u_xlatu1.z);
    u_xlat0.x = u_xlat6.x * u_xlat0.x;
    u_xlat0.x = u_xlat0.x * _CoCParams.z;
    u_xlat2.xz = u_xlat0.xx * vec2(-1.33333337, 1.33333337);
    u_xlat2.y = float(-0.0);
    u_xlat2.w = float(0.0);
    u_xlat1.xy = u_xlat2.xy + vs_TEXCOORD0.xy;
    u_xlat2.xy = u_xlat2.zw + vs_TEXCOORD0.xy;
    u_xlat10_3.xyz = texture(_SourceTex, u_xlat1.xyz).xyz;
    u_xlat10_3.w = texture(_HalfCoCTexture, u_xlat1.xyz).x;
    u_xlat16_4.x = u_xlat6.x + (-u_xlat10_3.w);
    u_xlat16_3 = u_xlat10_3 * vec4(0.352941185, 0.352941185, 0.352941185, 0.352941185);
    u_xlat1.xy = vs_TEXCOORD0.xy;
    u_xlat10_5.xyz = texture(_SourceTex, u_xlat1.xyz).xyz;
    u_xlat10_5.w = texture(_HalfCoCTexture, u_xlat1.xyz).x;
    u_xlat2.z = u_xlat1.z;
    u_xlat16_4.y = u_xlat6.x + (-u_xlat10_5.w);
    u_xlat16_4.xy = (-u_xlat16_4.xy) + vec2(1.0, 1.0);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_4.xy = min(max(u_xlat16_4.xy, 0.0), 1.0);
#else
    u_xlat16_4.xy = clamp(u_xlat16_4.xy, 0.0, 1.0);
#endif
    u_xlat16_1 = u_xlat10_5 * vec4(0.294117659, 0.294117659, 0.294117659, 0.294117659);
    u_xlat16_1 = u_xlat16_4.yyyy * u_xlat16_1;
    u_xlat16_1 = u_xlat16_3 * u_xlat16_4.xxxx + u_xlat16_1;
    u_xlat10_3.xyz = texture(_SourceTex, u_xlat2.xyz).xyz;
    u_xlat10_3.w = texture(_HalfCoCTexture, u_xlat2.xyz).x;
    u_xlat16_4.x = u_xlat6.x + (-u_xlat10_3.w);
    u_xlat16_4.x = (-u_xlat16_4.x) + 1.0;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_4.x = min(max(u_xlat16_4.x, 0.0), 1.0);
#else
    u_xlat16_4.x = clamp(u_xlat16_4.x, 0.0, 1.0);
#endif
    u_xlat16_0 = u_xlat10_3 * vec4(0.352941185, 0.352941185, 0.352941185, 0.352941185);
    u_xlat16_0 = u_xlat16_0 * u_xlat16_4.xxxx + u_xlat16_1;
    u_xlat16_4.x = u_xlat16_0.w + 9.99999975e-05;
    SV_Target0.xyz = u_xlat16_0.xyz / u_xlat16_4.xxx;
    SV_Target0.w = 1.0;
    return;
}

#endif
                              $Globals,         _SourceSize                          _DownSampleScaleFactor                       
   _CoCParams                               $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      _HalfCoCTexture           
      UnityStereoEyeIndices                  