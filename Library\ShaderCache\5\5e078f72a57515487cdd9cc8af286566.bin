d  <Q                             _SMAA_PRESET_MEDIUM v  #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 hlslcc_mtx4x4_FullscreenProjMat[4];
uniform 	vec4 _Metrics;
in highp vec4 in_POSITION0;
in highp vec2 in_TEXCOORD0;
out highp vec2 vs_TEXCOORD0;
out highp vec4 vs_TEXCOORD1;
out highp vec4 vs_TEXCOORD2;
out highp vec4 vs_TEXCOORD3;
vec4 u_xlat0;
void main()
{
    u_xlat0 = in_POSITION0.yyyy * hlslcc_mtx4x4_FullscreenProjMat[1];
    u_xlat0 = hlslcc_mtx4x4_FullscreenProjMat[0] * in_POSITION0.xxxx + u_xlat0;
    u_xlat0 = hlslcc_mtx4x4_FullscreenProjMat[2] * in_POSITION0.zzzz + u_xlat0;
    gl_Position = u_xlat0 + hlslcc_mtx4x4_FullscreenProjMat[3];
    vs_TEXCOORD0.xy = in_TEXCOORD0.xy;
    vs_TEXCOORD1 = _Metrics.xyxy * vec4(-1.0, 0.0, 0.0, -1.0) + in_TEXCOORD0.xyxy;
    vs_TEXCOORD2 = _Metrics.xyxy * vec4(1.0, 0.0, 0.0, 1.0) + in_TEXCOORD0.xyxy;
    vs_TEXCOORD3 = _Metrics.xyxy * vec4(-2.0, 0.0, 0.0, -2.0) + in_TEXCOORD0.xyxy;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
UNITY_LOCATION(0) uniform mediump sampler2D _ColorTexture;
in highp vec2 vs_TEXCOORD0;
in highp vec4 vs_TEXCOORD1;
in highp vec4 vs_TEXCOORD2;
in highp vec4 vs_TEXCOORD3;
layout(location = 0) out highp vec4 SV_Target0;
vec3 u_xlat0;
mediump vec3 u_xlat10_0;
bvec2 u_xlatb0;
vec3 u_xlat1;
mediump vec3 u_xlat10_1;
vec3 u_xlat2;
vec3 u_xlat3;
mediump vec3 u_xlat10_3;
vec3 u_xlat4;
mediump vec3 u_xlat10_4;
mediump vec3 u_xlat10_5;
vec2 u_xlat6;
float u_xlat12;
vec2 u_xlat14;
bvec2 u_xlatb14;
float u_xlat18;
bool u_xlatb18;
void main()
{
    u_xlat10_0.xyz = texture(_ColorTexture, vs_TEXCOORD0.xy).xyz;
    u_xlat10_1.xyz = texture(_ColorTexture, vs_TEXCOORD1.xy).xyz;
    u_xlat2.xyz = abs(u_xlat10_0.xyz) + -abs(u_xlat10_1.xyz);
    u_xlat18 = max(abs(u_xlat2.y), abs(u_xlat2.x));
    u_xlat2.x = max(abs(u_xlat2.z), u_xlat18);
    u_xlat10_3.xyz = texture(_ColorTexture, vs_TEXCOORD1.zw).xyz;
    u_xlat4.xyz = abs(u_xlat10_0.xyz) + -abs(u_xlat10_3.xyz);
    u_xlat18 = max(abs(u_xlat4.y), abs(u_xlat4.x));
    u_xlat2.y = max(abs(u_xlat4.z), u_xlat18);
    u_xlatb14.xy = greaterThanEqual(u_xlat2.xyxy, vec4(0.100000001, 0.100000001, 0.100000001, 0.100000001)).xy;
    u_xlat14.x = u_xlatb14.x ? float(1.0) : 0.0;
    u_xlat14.y = u_xlatb14.y ? float(1.0) : 0.0;
;
    u_xlat18 = dot(u_xlat14.xy, vec2(1.0, 1.0));
#ifdef UNITY_ADRENO_ES3
    u_xlatb18 = !!(u_xlat18==0.0);
#else
    u_xlatb18 = u_xlat18==0.0;
#endif
    if(u_xlatb18){discard;}
    u_xlat10_4.xyz = texture(_ColorTexture, vs_TEXCOORD2.xy).xyz;
    u_xlat4.xyz = abs(u_xlat10_0.xyz) + -abs(u_xlat10_4.xyz);
    u_xlat18 = max(abs(u_xlat4.y), abs(u_xlat4.x));
    u_xlat4.x = max(abs(u_xlat4.z), u_xlat18);
    u_xlat10_5.xyz = texture(_ColorTexture, vs_TEXCOORD2.zw).xyz;
    u_xlat0.xyz = abs(u_xlat10_0.xyz) + -abs(u_xlat10_5.xyz);
    u_xlat0.x = max(abs(u_xlat0.y), abs(u_xlat0.x));
    u_xlat4.y = max(abs(u_xlat0.z), u_xlat0.x);
    u_xlat0.xy = max(u_xlat2.xy, u_xlat4.xy);
    u_xlat10_4.xyz = texture(_ColorTexture, vs_TEXCOORD3.xy).xyz;
    u_xlat1.xyz = abs(u_xlat10_1.xyz) + -abs(u_xlat10_4.xyz);
    u_xlat12 = max(abs(u_xlat1.y), abs(u_xlat1.x));
    u_xlat1.x = max(abs(u_xlat1.z), u_xlat12);
    u_xlat10_4.xyz = texture(_ColorTexture, vs_TEXCOORD3.zw).xyz;
    u_xlat3.xyz = abs(u_xlat10_3.xyz) + -abs(u_xlat10_4.xyz);
    u_xlat12 = max(abs(u_xlat3.y), abs(u_xlat3.x));
    u_xlat1.y = max(abs(u_xlat3.z), u_xlat12);
    u_xlat0.xy = max(u_xlat0.xy, u_xlat1.xy);
    u_xlat0.x = max(u_xlat0.y, u_xlat0.x);
    u_xlat6.xy = u_xlat2.xy + u_xlat2.xy;
    u_xlatb0.xy = greaterThanEqual(u_xlat6.xyxx, u_xlat0.xxxx).xy;
    u_xlat0.x = u_xlatb0.x ? float(1.0) : 0.0;
    u_xlat0.y = u_xlatb0.y ? float(1.0) : 0.0;
;
    u_xlat0.xy = u_xlat0.xy * u_xlat14.xy;
    SV_Target0.xy = u_xlat0.xy;
    SV_Target0.zw = vec2(0.0, 0.0);
    return;
}

#endif
                               $GlobalsP         _Metrics                  @      _FullscreenProjMat                              
   _ColorTexture                      