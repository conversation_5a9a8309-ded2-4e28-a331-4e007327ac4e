P%  <Q                         _USE_DRAW_PROCEDURAL   	   _BLOOM_LQ   #  #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
out highp vec2 vs_TEXCOORD0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	vec4 _Bloom_Params;
uniform 	float _Bloom_RGBM;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
UNITY_LOCATION(0) uniform mediump sampler2D _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _Bloom_Texture;
UNITY_LOCATION(2) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(3) uniform mediump sampler2D _UserLut;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec3 u_xlat0;
mediump vec4 u_xlat10_0;
bvec3 u_xlatb0;
mediump vec3 u_xlat16_1;
vec3 u_xlat2;
mediump vec3 u_xlat16_2;
mediump vec3 u_xlat16_3;
vec3 u_xlat4;
mediump vec3 u_xlat16_4;
vec3 u_xlat5;
mediump vec3 u_xlat10_5;
vec2 u_xlat6;
mediump vec3 u_xlat10_7;
float u_xlat21;
bool u_xlatb21;
void main()
{
    u_xlat0.xyz = texture(_SourceTex, vs_TEXCOORD0.xy).xyz;
    u_xlat16_1.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
    u_xlat16_2.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
    u_xlat16_2.xyz = u_xlat16_2.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
    u_xlat16_2.xyz = log2(abs(u_xlat16_2.xyz));
    u_xlat16_2.xyz = u_xlat16_2.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
    u_xlat16_2.xyz = exp2(u_xlat16_2.xyz);
    u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
    {
        vec3 hlslcc_movcTemp = u_xlat16_1;
        hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_1.x : u_xlat16_2.x;
        hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_1.y : u_xlat16_2.y;
        hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_1.z : u_xlat16_2.z;
        u_xlat16_1 = hlslcc_movcTemp;
    }
    u_xlat10_0 = texture(_Bloom_Texture, vs_TEXCOORD0.xy);
    u_xlat16_2.xyz = u_xlat10_0.xyz * u_xlat10_0.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_Bloom_RGBM);
#else
    u_xlatb0.x = 0.0<_Bloom_RGBM;
#endif
    if(u_xlatb0.x){
        u_xlat16_3.xyz = u_xlat10_0.www * u_xlat16_2.xyz;
        u_xlat2.xyz = u_xlat16_3.xyz * vec3(8.0, 8.0, 8.0);
        u_xlat16_2.xyz = u_xlat2.xyz;
    }
    u_xlat0.xyz = u_xlat16_2.xyz * _Bloom_Params.xxx;
    u_xlat0.xyz = u_xlat0.xyz * _Bloom_Params.yzw + u_xlat16_1.xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlatb21 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb21 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb21){
        u_xlat4.xy = vs_TEXCOORD0.xy + (-_Vignette_Params2.xy);
        u_xlat4.yz = abs(u_xlat4.xy) * _Vignette_Params2.zz;
        u_xlat4.x = u_xlat4.y * _Vignette_Params1.w;
        u_xlat21 = dot(u_xlat4.xz, u_xlat4.xz);
        u_xlat21 = (-u_xlat21) + 1.0;
        u_xlat21 = max(u_xlat21, 0.0);
        u_xlat21 = log2(u_xlat21);
        u_xlat21 = u_xlat21 * _Vignette_Params2.w;
        u_xlat21 = exp2(u_xlat21);
        u_xlat4.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat4.xyz = vec3(u_xlat21) * u_xlat4.xyz + _Vignette_Params1.xyz;
        u_xlat4.xyz = u_xlat0.xyz * u_xlat4.xyz;
        u_xlat16_4.xyz = u_xlat4.xyz;
    } else {
        u_xlat16_4.xyz = u_xlat0.xyz;
    }
    u_xlat16_1.xyz = u_xlat16_4.xyz * _Lut_Params.www;
#ifdef UNITY_ADRENO_ES3
    u_xlat16_1.xyz = min(max(u_xlat16_1.xyz, 0.0), 1.0);
#else
    u_xlat16_1.xyz = clamp(u_xlat16_1.xyz, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb0.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb0.x){
        u_xlat16_2.xyz = u_xlat16_1.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_3.xyz = log2(u_xlat16_1.xyz);
        u_xlat16_3.xyz = u_xlat16_3.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_3.xyz = exp2(u_xlat16_3.xyz);
        u_xlat16_3.xyz = u_xlat16_3.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_1.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_2;
            hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_2.x : u_xlat16_3.x;
            hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_2.y : u_xlat16_3.y;
            hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_2.z : u_xlat16_3.z;
            u_xlat16_2 = hlslcc_movcTemp;
        }
        u_xlat0.xyz = u_xlat16_2.zxy * _UserLut_Params.zzz;
        u_xlat0.x = floor(u_xlat0.x);
        u_xlat5.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat5.yz = u_xlat0.yz * _UserLut_Params.xy + u_xlat5.xy;
        u_xlat5.x = u_xlat0.x * _UserLut_Params.y + u_xlat5.y;
        u_xlat10_7.xyz = textureLod(_UserLut, u_xlat5.xz, 0.0).xyz;
        u_xlat6.x = _UserLut_Params.y;
        u_xlat6.y = 0.0;
        u_xlat5.xy = u_xlat5.xz + u_xlat6.xy;
        u_xlat10_5.xyz = textureLod(_UserLut, u_xlat5.xy, 0.0).xyz;
        u_xlat0.x = u_xlat16_2.z * _UserLut_Params.z + (-u_xlat0.x);
        u_xlat5.xyz = (-u_xlat10_7.xyz) + u_xlat10_5.xyz;
        u_xlat0.xyz = u_xlat0.xxx * u_xlat5.xyz + u_xlat10_7.xyz;
        u_xlat0.xyz = (-u_xlat16_2.xyz) + u_xlat0.xyz;
        u_xlat0.xyz = _UserLut_Params.www * u_xlat0.xyz + u_xlat16_2.xyz;
        u_xlat16_2.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_3.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_3.xyz = u_xlat16_3.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_3.xyz = log2(abs(u_xlat16_3.xyz));
        u_xlat16_3.xyz = u_xlat16_3.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_3.xyz = exp2(u_xlat16_3.xyz);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
        u_xlat16_1.x = (u_xlatb0.x) ? u_xlat16_2.x : u_xlat16_3.x;
        u_xlat16_1.y = (u_xlatb0.y) ? u_xlat16_2.y : u_xlat16_3.y;
        u_xlat16_1.z = (u_xlatb0.z) ? u_xlat16_2.z : u_xlat16_3.z;
    }
    u_xlat0.xyz = u_xlat16_1.zxy * _Lut_Params.zzz;
    u_xlat0.x = floor(u_xlat0.x);
    u_xlat5.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat5.yz = u_xlat0.yz * _Lut_Params.xy + u_xlat5.xy;
    u_xlat5.x = u_xlat0.x * _Lut_Params.y + u_xlat5.y;
    u_xlat10_7.xyz = textureLod(_InternalLut, u_xlat5.xz, 0.0).xyz;
    u_xlat6.x = _Lut_Params.y;
    u_xlat6.y = 0.0;
    u_xlat5.xy = u_xlat5.xz + u_xlat6.xy;
    u_xlat10_5.xyz = textureLod(_InternalLut, u_xlat5.xy, 0.0).xyz;
    u_xlat0.x = u_xlat16_1.z * _Lut_Params.z + (-u_xlat0.x);
    u_xlat5.xyz = (-u_xlat10_7.xyz) + u_xlat10_5.xyz;
    u_xlat0.xyz = u_xlat0.xxx * u_xlat5.xyz + u_xlat10_7.xyz;
    u_xlat16_1.xyz = u_xlat0.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlat16_2.xyz = log2(abs(u_xlat0.xyz));
    u_xlat16_2.xyz = u_xlat16_2.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_2.xyz = exp2(u_xlat16_2.xyz);
    u_xlat16_2.xyz = u_xlat16_2.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat0.xyzx).xyz;
    SV_Target0.x = (u_xlatb0.x) ? u_xlat16_1.x : u_xlat16_2.x;
    SV_Target0.y = (u_xlatb0.y) ? u_xlat16_1.y : u_xlat16_2.y;
    SV_Target0.z = (u_xlatb0.z) ? u_xlat16_1.z : u_xlat16_2.z;
    SV_Target0.w = 1.0;
    return;
}

#endif
                                 $Globals`         _Lut_Params                          _UserLut_Params                      
   _Bloom_Params                            _Bloom_RGBM                   0      _Vignette_Params1                     @      _Vignette_Params2                     P          $Globals      
   _ScaleBias                               
   _SourceTex                    _Bloom_Texture                  _InternalLut                _UserLut                 