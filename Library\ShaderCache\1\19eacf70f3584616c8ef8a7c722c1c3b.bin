d  <Q                          
   _ORTHOGRAPHIC      _SOURCE_DEPTH_NORMALS   >  #ifdef VERTEX
#version 300 es

in highp vec4 in_POSITION0;
in highp vec2 in_TEXCOORD0;
out highp vec2 vs_TEXCOORD0;
void main()
{
    gl_Position.xyz = in_POSITION0.xyz;
    gl_Position.w = 1.0;
    vs_TEXCOORD0.xy = in_TEXCOORD0.xy + vec2(9.99999997e-07, 9.99999997e-07);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SSAOParams;
uniform 	vec4 _SourceSize;
UNITY_LOCATION(0) uniform highp sampler2D _CameraNormalsTexture;
UNITY_LOCATION(1) uniform mediump sampler2D _BaseMap;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec4 u_xlat16_0;
bool u_xlatb0;
vec4 u_xlat1;
mediump vec4 u_xlat16_1;
mediump float u_xlat16_2;
float u_xlat3;
mediump vec4 u_xlat16_3;
vec3 u_xlat4;
mediump vec4 u_xlat16_4;
mediump vec3 u_xlat16_7;
vec3 u_xlat8;
vec3 u_xlat9;
float u_xlat13;
mediump float u_xlat16_13;
float u_xlat18;
void main()
{
    u_xlat16_0.xy = texture(_CameraNormalsTexture, vs_TEXCOORD0.xy).xy;
    u_xlat16_1.x = u_xlat16_0.y * 0.5 + 0.5;
    u_xlat16_1.x = -abs(u_xlat16_0.x) + u_xlat16_1.x;
    u_xlat16_2 = -abs(u_xlat16_1.x) + 1.0;
    u_xlat16_1.y = u_xlat16_0.y + (-u_xlat16_1.x);
#ifdef UNITY_ADRENO_ES3
    u_xlatb0 = !!(u_xlat16_0.x>=0.0);
#else
    u_xlatb0 = u_xlat16_0.x>=0.0;
#endif
    u_xlat16_2 = -abs(u_xlat16_1.y) + u_xlat16_2;
    u_xlat16_2 = max(u_xlat16_2, 0.00048828125);
    u_xlat1.z = (u_xlatb0) ? u_xlat16_2 : (-u_xlat16_2);
    u_xlat1.xy = u_xlat16_1.xy;
    u_xlat16_2 = dot(u_xlat1.xyz, u_xlat1.xyz);
    u_xlat16_2 = inversesqrt(u_xlat16_2);
    u_xlat16_7.xyz = u_xlat1.xyz * vec3(u_xlat16_2);
    u_xlat0.x = float(1.0) / float(_SSAOParams.z);
    u_xlat0.x = u_xlat0.x * _SourceSize.z;
    u_xlat0.xz = u_xlat0.xx * vec2(2.76923084, 6.46153831);
    u_xlat0.y = float(0.0);
    u_xlat0.w = float(0.0);
    u_xlat1 = (-u_xlat0) + vs_TEXCOORD0.xyxy;
    u_xlat0 = u_xlat0 + vs_TEXCOORD0.xyxy;
    u_xlat16_3 = texture(_BaseMap, u_xlat1.xy);
    u_xlat16_1 = texture(_BaseMap, u_xlat1.zw);
    u_xlat8.xyz = u_xlat16_3.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat8.x = dot(u_xlat16_7.xyz, u_xlat8.xyz);
    u_xlat8.x = u_xlat8.x + -0.800000012;
    u_xlat8.x = u_xlat8.x * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat8.x = min(max(u_xlat8.x, 0.0), 1.0);
#else
    u_xlat8.x = clamp(u_xlat8.x, 0.0, 1.0);
#endif
    u_xlat13 = u_xlat8.x * -2.0 + 3.0;
    u_xlat8.x = u_xlat8.x * u_xlat8.x;
    u_xlat8.x = u_xlat8.x * u_xlat13;
    u_xlat8.x = u_xlat8.x * 0.31621623;
    u_xlat3 = u_xlat8.x * u_xlat16_3.x;
    u_xlat16_13 = texture(_BaseMap, vs_TEXCOORD0.xy).x;
    u_xlat3 = u_xlat16_13 * 0.227027029 + u_xlat3;
    u_xlat16_4 = texture(_BaseMap, u_xlat0.xy);
    u_xlat16_0 = texture(_BaseMap, u_xlat0.zw);
    u_xlat9.xyz = u_xlat16_4.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat13 = dot(u_xlat16_7.xyz, u_xlat9.xyz);
    u_xlat13 = u_xlat13 + -0.800000012;
    u_xlat13 = u_xlat13 * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat13 = min(max(u_xlat13, 0.0), 1.0);
#else
    u_xlat13 = clamp(u_xlat13, 0.0, 1.0);
#endif
    u_xlat18 = u_xlat13 * -2.0 + 3.0;
    u_xlat13 = u_xlat13 * u_xlat13;
    u_xlat13 = u_xlat13 * u_xlat18;
    u_xlat18 = u_xlat13 * 0.31621623;
    u_xlat8.x = u_xlat13 * 0.31621623 + u_xlat8.x;
    u_xlat3 = u_xlat16_4.x * u_xlat18 + u_xlat3;
    u_xlat4.xyz = u_xlat16_1.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat13 = dot(u_xlat16_7.xyz, u_xlat4.xyz);
    u_xlat13 = u_xlat13 + -0.800000012;
    u_xlat13 = u_xlat13 * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat13 = min(max(u_xlat13, 0.0), 1.0);
#else
    u_xlat13 = clamp(u_xlat13, 0.0, 1.0);
#endif
    u_xlat18 = u_xlat13 * -2.0 + 3.0;
    u_xlat13 = u_xlat13 * u_xlat13;
    u_xlat13 = u_xlat13 * u_xlat18;
    u_xlat18 = u_xlat13 * 0.0702702701;
    u_xlat8.x = u_xlat13 * 0.0702702701 + u_xlat8.x;
    u_xlat3 = u_xlat16_1.x * u_xlat18 + u_xlat3;
    u_xlat4.xyz = u_xlat16_0.yzw * vec3(2.0, 2.0, 2.0) + vec3(-1.0, -1.0, -1.0);
    u_xlat13 = dot(u_xlat16_7.xyz, u_xlat4.xyz);
    u_xlat1.yzw = u_xlat16_7.xyz * vec3(0.5, 0.5, 0.5) + vec3(0.5, 0.5, 0.5);
    u_xlat13 = u_xlat13 + -0.800000012;
    u_xlat13 = u_xlat13 * 5.00000048;
#ifdef UNITY_ADRENO_ES3
    u_xlat13 = min(max(u_xlat13, 0.0), 1.0);
#else
    u_xlat13 = clamp(u_xlat13, 0.0, 1.0);
#endif
    u_xlat18 = u_xlat13 * -2.0 + 3.0;
    u_xlat13 = u_xlat13 * u_xlat13;
    u_xlat13 = u_xlat13 * u_xlat18;
    u_xlat18 = u_xlat13 * 0.0702702701;
    u_xlat8.x = u_xlat13 * 0.0702702701 + u_xlat8.x;
    u_xlat8.x = u_xlat8.x + 0.227027029;
    u_xlat8.x = float(1.0) / float(u_xlat8.x);
    u_xlat3 = u_xlat16_0.x * u_xlat18 + u_xlat3;
    u_xlat1.x = u_xlat8.x * u_xlat3;
    SV_Target0 = u_xlat1;
    return;
}

#endif
                               $Globals          _SSAOParams                          _SourceSize                                _CameraNormalsTexture                     _BaseMap                 