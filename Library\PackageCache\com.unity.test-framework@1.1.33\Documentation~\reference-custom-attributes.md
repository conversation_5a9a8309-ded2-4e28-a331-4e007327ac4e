# Custom attributes

As a part of UTF’s public API we provide the following attributes:

* [ConditionalIgnore attribute](./reference-attribute-conditionalignore.md)
* [PostBuildCleanup attribute](./reference-setup-and-cleanup.md#prebuildsetup-and-postbuildcleanup)
* [PrebuildSetup attribute](./reference-setup-and-cleanup.md#prebuildsetup-and-postbuildcleanup)
* [TestMustExpectAllLogs attribute](./reference-attribute-testmustexpectalllogs.md)
* [TestPlayerBuildModifier attribute](./reference-attribute-testplayerbuildmodifier.md)
* [TestRunCallback attribute](./reference-attribute-testruncallback.md)
* [UnityPlatform attribute](./reference-attribute-unityplatform.md)
* [UnitySetUp attribute](./reference-actions-outside-tests.md#unitysetup-and-unityteardown)
* [UnityTearDown attribute](./reference-actions-outside-tests.md#unitysetup-and-unityteardown)
* [UnityTest attribute](./reference-attribute-unitytest.md)

