L  <Q                         _USE_DRAW_PROCEDURAL      #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
out highp vec2 vs_TEXCOORD0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _SourceSize;
uniform 	vec4 _CoCParams;
UNITY_LOCATION(0) uniform mediump sampler2D _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _FullCoCTexture;
in highp vec2 vs_TEXCOORD0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec3 u_xlat10_0;
vec4 u_xlat1;
mediump float u_xlat10_1;
bool u_xlatb1;
vec4 u_xlat2;
mediump vec3 u_xlat10_2;
mediump vec3 u_xlat16_3;
float u_xlat4;
float u_xlat6;
mediump float u_xlat10_6;
mediump float u_xlat16_8;
float u_xlat9;
mediump vec3 u_xlat10_9;
void main()
{
    u_xlat0 = (-_SourceSize.zwzw) * vec4(0.5, 0.5, -0.5, 0.5) + vs_TEXCOORD0.xyxy;
    u_xlat10_1 = texture(_FullCoCTexture, u_xlat0.zw).x;
    u_xlat1.x = u_xlat10_1 * 2.0 + -1.0;
    u_xlat2 = _SourceSize.zwzw * vec4(-0.5, 0.5, 0.5, 0.5) + vs_TEXCOORD0.xyxy;
    u_xlat10_6 = texture(_FullCoCTexture, u_xlat2.xy).x;
    u_xlat6 = u_xlat10_6 * 2.0 + -1.0;
    u_xlat16_3.x = min(u_xlat6, u_xlat1.x);
    u_xlat16_8 = max(u_xlat6, u_xlat1.x);
    u_xlat10_1 = texture(_FullCoCTexture, u_xlat2.zw).x;
    u_xlat1.x = u_xlat10_1 * 2.0 + -1.0;
    u_xlat16_3.x = min(u_xlat1.x, u_xlat16_3.x);
    u_xlat16_8 = max(u_xlat1.x, u_xlat16_8);
    u_xlat10_1 = texture(_FullCoCTexture, u_xlat0.xy).x;
    u_xlat1.x = u_xlat10_1 * 2.0 + -1.0;
    u_xlat16_3.x = min(u_xlat16_3.x, u_xlat1.x);
    u_xlat16_8 = max(u_xlat16_8, u_xlat1.x);
#ifdef UNITY_ADRENO_ES3
    u_xlatb1 = !!(u_xlat16_8<(-u_xlat16_3.x));
#else
    u_xlatb1 = u_xlat16_8<(-u_xlat16_3.x);
#endif
    u_xlat16_3.x = (u_xlatb1) ? u_xlat16_3.x : u_xlat16_8;
    u_xlat1.w = u_xlat16_3.x * _CoCParams.z;
    u_xlat4 = _SourceSize.w + _SourceSize.w;
    u_xlat4 = float(1.0) / u_xlat4;
    u_xlat4 = abs(u_xlat1.w) * u_xlat4;
#ifdef UNITY_ADRENO_ES3
    u_xlat4 = min(max(u_xlat4, 0.0), 1.0);
#else
    u_xlat4 = clamp(u_xlat4, 0.0, 1.0);
#endif
    u_xlat9 = u_xlat4 * -2.0 + 3.0;
    u_xlat4 = u_xlat4 * u_xlat4;
    u_xlat4 = u_xlat4 * u_xlat9;
    u_xlat10_9.xyz = texture(_SourceTex, u_xlat0.xy).xyz;
    u_xlat10_0.xyz = texture(_SourceTex, u_xlat0.zw).xyz;
    u_xlat16_3.xyz = u_xlat10_0.xyz + u_xlat10_9.xyz;
    u_xlat10_0.xyz = texture(_SourceTex, u_xlat2.xy).xyz;
    u_xlat10_2.xyz = texture(_SourceTex, u_xlat2.zw).xyz;
    u_xlat16_3.xyz = u_xlat10_0.xyz + u_xlat16_3.xyz;
    u_xlat16_3.xyz = u_xlat10_2.xyz + u_xlat16_3.xyz;
    u_xlat16_3.xyz = u_xlat16_3.xyz * vec3(0.25, 0.25, 0.25);
    u_xlat1.xyz = vec3(u_xlat4) * u_xlat16_3.xyz;
    SV_Target0 = u_xlat1;
    return;
}

#endif
                                 $Globals          _SourceSize                       
   _CoCParams                              $Globals      
   _ScaleBias                               
   _SourceTex                    _FullCoCTexture                  