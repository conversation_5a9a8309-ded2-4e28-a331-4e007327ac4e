{"root": [{"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Management", "nameSpace": "UnityEngine.XR.Management", "className": "XRGeneralSettings", "methodName": "AttemptInitializeXRSDKOnLoad", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.XR.Management", "nameSpace": "UnityEngine.XR.Management", "className": "XRGeneralSettings", "methodName": "AttemptStartXRSDKOnBeforeSplashScreen", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "glTFast", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Unity.RenderPipelines.Universal.Runtime", "nameSpace": "UnityEngine.Rendering.Universal", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Simulation", "className": "SimulatedInputLayoutLoader", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Interactions", "className": "SectorInteraction", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Composites", "className": "Vector3FallbackComposite", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.XR.Interaction.Toolkit", "nameSpace": "UnityEngine.XR.Interaction.Toolkit.Inputs.Composites", "className": "QuaternionFallbackComposite", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "RhinoXDebugger", "nameSpace": "RhinoXDebugger", "className": "DebuggerGlobalSetting", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "RhinoXDebugger", "nameSpace": "RhinoXDebugger.Asyncoroutine", "className": "AwaiterCoroutineer", "methodName": "Install", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "RhinoXDebugger", "nameSpace": "RhinoXDebugger", "className": "EyeBufferFrame", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "RhinoX-Unity", "nameSpace": "Ximmerse.RhinoX", "className": "RhinoXSystem", "methodName": "InitializeParamsLoader", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "RhinoX-Unity", "nameSpace": "Ximmerse.RhinoX", "className": "RhinoXGlobalSetting", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "RhinoX-Unity", "nameSpace": "Ximmerse.RhinoX.Internal", "className": "AwaiterCoroutineer", "methodName": "Install", "loadTypes": 1, "isUnityClass": false}]}