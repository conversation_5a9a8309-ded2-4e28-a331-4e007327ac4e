t  <Q                         _USE_DRAW_PROCEDURAL      _SMAA_PRESET_LOW  #ifdef VERTEX
#version 300 es

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
uniform 	vec4 _Metrics;
out highp vec2 vs_TEXCOORD0;
out highp vec4 vs_TEXCOORD1;
vec2 u_xlat0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
uint u_xlatu4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    u_xlat0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati4 = u_xlati4 + 1;
    u_xlatu4 = uint(uint(u_xlati4) & 1u);
    u_xlat1.y = float(u_xlatu4);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    vs_TEXCOORD0.xy = u_xlat0.xy;
    vs_TEXCOORD1 = _Metrics.xyxy * vec4(1.0, 0.0, 0.0, 1.0) + u_xlat0.xyxy;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Metrics;
UNITY_LOCATION(0) uniform mediump sampler2D _ColorTexture;
UNITY_LOCATION(1) uniform mediump sampler2D _BlendTexture;
in highp vec2 vs_TEXCOORD0;
in highp vec4 vs_TEXCOORD1;
layout(location = 0) out highp vec4 SV_Target0;
vec4 u_xlat0;
vec4 u_xlat1;
mediump vec4 u_xlat10_1;
bool u_xlatb1;
vec4 u_xlat2;
mediump vec4 u_xlat10_2;
float u_xlat6;
void main()
{
    u_xlat0.x = texture(_BlendTexture, vs_TEXCOORD1.xy).w;
    u_xlat0.y = texture(_BlendTexture, vs_TEXCOORD1.zw).y;
    u_xlat0.zw = texture(_BlendTexture, vs_TEXCOORD0.xy).zx;
    u_xlat1.x = dot(u_xlat0, vec4(1.0, 1.0, 1.0, 1.0));
#ifdef UNITY_ADRENO_ES3
    u_xlatb1 = !!(u_xlat1.x<9.99999975e-06);
#else
    u_xlatb1 = u_xlat1.x<9.99999975e-06;
#endif
    if(u_xlatb1){
        SV_Target0 = textureLod(_ColorTexture, vs_TEXCOORD0.xy, 0.0);
    } else {
        u_xlat1.xy = max(u_xlat0.zw, u_xlat0.xy);
#ifdef UNITY_ADRENO_ES3
        u_xlatb1 = !!(u_xlat1.y<u_xlat1.x);
#else
        u_xlatb1 = u_xlat1.y<u_xlat1.x;
#endif
        u_xlat2.xz = bool(u_xlatb1) ? u_xlat0.xz : vec2(0.0, 0.0);
        u_xlat2.yw = (bool(u_xlatb1)) ? vec2(0.0, 0.0) : u_xlat0.yw;
        u_xlat0.x = (u_xlatb1) ? u_xlat0.x : u_xlat0.y;
        u_xlat0.y = (u_xlatb1) ? u_xlat0.z : u_xlat0.w;
        u_xlat6 = dot(u_xlat0.xy, vec2(1.0, 1.0));
        u_xlat0.xy = u_xlat0.xy / vec2(u_xlat6);
        u_xlat1 = _Metrics.xyxy * vec4(1.0, 1.0, -1.0, -1.0);
        u_xlat1 = u_xlat2 * u_xlat1 + vs_TEXCOORD0.xyxy;
        u_xlat10_2 = textureLod(_ColorTexture, u_xlat1.xy, 0.0);
        u_xlat10_1 = textureLod(_ColorTexture, u_xlat1.zw, 0.0);
        u_xlat1 = u_xlat0.yyyy * u_xlat10_1;
        SV_Target0 = u_xlat0.xxxx * u_xlat10_2 + u_xlat1;
    }
    return;
}

#endif
                                 $Globals         _Metrics                             $Globals       
   _ScaleBias                           _Metrics                            
   _ColorTexture                  
   _BlendTexture                    