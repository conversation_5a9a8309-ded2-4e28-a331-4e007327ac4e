HM  <Q                         STEREO_MULTIVIEW_ON    _USE_DRAW_PROCEDURAL      _DISTORTION    _LINEAR_TO_SRGB_CONVERSION  
   _TONEMAP_ACES   DJ  #ifdef VERTEX
#version 300 es
#extension GL_OVR_multiview2 : require

#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _ScaleBias;
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
UNITY_BINDING(0) uniform UnityStereoEyeIndices {
#endif
	UNITY_UNIFORM vec4 unity_StereoEyeIndices[2];
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
};
#endif
layout(num_views = 2) in;
out highp vec2 vs_TEXCOORD0;
out highp float vs_BLENDWEIGHT0;
int u_xlati0;
uvec2 u_xlatu0;
vec3 u_xlat1;
int u_xlati4;
void main()
{
    u_xlati0 = int(uint(uint(gl_VertexID) & 1u));
    u_xlatu0.y = uint(uint(gl_VertexID) >> 1u);
    u_xlati4 = (-u_xlati0) + (-int(u_xlatu0.y));
    u_xlati0 = u_xlati0 + int(u_xlatu0.y);
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.xz = vec2(u_xlatu0.yx);
    vs_TEXCOORD0.xy = u_xlat1.xz * _ScaleBias.xy + _ScaleBias.zw;
    u_xlati0 = u_xlati4 + 1;
    u_xlatu0.x = uint(uint(u_xlati0) & 1u);
    u_xlat1.y = float(u_xlatu0.x);
    gl_Position.xy = u_xlat1.xy * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
    gl_Position.zw = vec2(-1.0, 1.0);
    u_xlatu0.x = gl_ViewID_OVR;
    vs_BLENDWEIGHT0 = unity_StereoEyeIndices[int(u_xlatu0.x)].x;
    return;
}

#endif
#ifdef FRAGMENT
#version 300 es
#ifdef GL_EXT_shader_texture_lod
#extension GL_EXT_shader_texture_lod : enable
#endif

precision highp float;
precision highp int;
#define HLSLCC_ENABLE_UNIFORM_BUFFERS 1
#if HLSLCC_ENABLE_UNIFORM_BUFFERS
#define UNITY_UNIFORM
#else
#define UNITY_UNIFORM uniform
#endif
#define UNITY_SUPPORTS_UNIFORM_LOCATION 1
#if UNITY_SUPPORTS_UNIFORM_LOCATION
#define UNITY_LOCATION(x) layout(location = x)
#define UNITY_BINDING(x) layout(binding = x, std140)
#else
#define UNITY_LOCATION(x)
#define UNITY_BINDING(x) layout(std140)
#endif
uniform 	vec4 _Lut_Params;
uniform 	vec4 _UserLut_Params;
uniform 	vec4 _Distortion_Params1;
uniform 	vec4 _Distortion_Params2;
uniform 	mediump vec4 _Vignette_Params1;
uniform 	vec4 _Vignette_Params2;
UNITY_LOCATION(0) uniform mediump sampler2DArray _SourceTex;
UNITY_LOCATION(1) uniform mediump sampler2D _InternalLut;
UNITY_LOCATION(2) uniform mediump sampler2D _UserLut;
in highp vec2 vs_TEXCOORD0;
in highp float vs_BLENDWEIGHT0;
layout(location = 0) out mediump vec4 SV_Target0;
vec4 u_xlat0;
mediump vec3 u_xlat10_0;
uint u_xlatu0;
bvec3 u_xlatb0;
vec3 u_xlat1;
mediump vec3 u_xlat16_1;
vec3 u_xlat2;
mediump vec3 u_xlat10_2;
bvec2 u_xlatb2;
vec3 u_xlat3;
mediump vec3 u_xlat16_4;
mediump vec3 u_xlat16_5;
mediump vec3 u_xlat16_6;
vec3 u_xlat7;
mediump vec3 u_xlat10_7;
bool u_xlatb7;
float u_xlat9;
bool u_xlatb9;
bool u_xlatb10;
mediump vec3 u_xlat16_11;
mediump vec2 u_xlat16_12;
bool u_xlatb14;
vec2 u_xlat15;
bool u_xlatb15;
float u_xlat16;
bool u_xlatb16;
mediump float u_xlat16_18;
mediump float u_xlat16_19;
float u_xlat21;
bool u_xlatb21;
float u_xlat22;
float u_xlat23;
bool u_xlatb23;
mediump float u_xlat16_25;
mediump float u_xlat16_26;
void main()
{
    u_xlatu0 = uint(vs_BLENDWEIGHT0);
    u_xlat7.xy = vs_TEXCOORD0.xy + vec2(-0.5, -0.5);
    u_xlat1.xy = u_xlat7.xy * _Distortion_Params2.zz + vec2(0.5, 0.5);
    u_xlat7.xy = u_xlat7.xy * _Distortion_Params2.zz + (-_Distortion_Params1.xy);
    u_xlat7.xy = u_xlat7.xy * _Distortion_Params1.zw;
    u_xlat21 = dot(u_xlat7.xy, u_xlat7.xy);
    u_xlat21 = sqrt(u_xlat21);
#ifdef UNITY_ADRENO_ES3
    u_xlatb15 = !!(0.0<_Distortion_Params2.w);
#else
    u_xlatb15 = 0.0<_Distortion_Params2.w;
#endif
    if(u_xlatb15){
        u_xlat15.xy = vec2(u_xlat21) * _Distortion_Params2.xy;
        u_xlat2.x = sin(u_xlat15.x);
        u_xlat3.x = cos(u_xlat15.x);
        u_xlat15.x = u_xlat2.x / u_xlat3.x;
        u_xlat22 = float(1.0) / float(u_xlat15.y);
        u_xlat15.x = u_xlat15.x * u_xlat22 + -1.0;
        u_xlat2.xy = u_xlat7.xy * u_xlat15.xx + u_xlat1.xy;
    } else {
        u_xlat15.x = float(1.0) / float(u_xlat21);
        u_xlat15.x = u_xlat15.x * _Distortion_Params2.x;
        u_xlat21 = u_xlat21 * _Distortion_Params2.y;
        u_xlat22 = min(abs(u_xlat21), 1.0);
        u_xlat23 = max(abs(u_xlat21), 1.0);
        u_xlat23 = float(1.0) / u_xlat23;
        u_xlat22 = u_xlat22 * u_xlat23;
        u_xlat23 = u_xlat22 * u_xlat22;
        u_xlat3.x = u_xlat23 * 0.0208350997 + -0.0851330012;
        u_xlat3.x = u_xlat23 * u_xlat3.x + 0.180141002;
        u_xlat3.x = u_xlat23 * u_xlat3.x + -0.330299497;
        u_xlat23 = u_xlat23 * u_xlat3.x + 0.999866009;
        u_xlat3.x = u_xlat22 * u_xlat23;
#ifdef UNITY_ADRENO_ES3
        u_xlatb10 = !!(1.0<abs(u_xlat21));
#else
        u_xlatb10 = 1.0<abs(u_xlat21);
#endif
        u_xlat3.x = u_xlat3.x * -2.0 + 1.57079637;
        u_xlat3.x = u_xlatb10 ? u_xlat3.x : float(0.0);
        u_xlat22 = u_xlat22 * u_xlat23 + u_xlat3.x;
        u_xlat21 = min(u_xlat21, 1.0);
#ifdef UNITY_ADRENO_ES3
        u_xlatb21 = !!(u_xlat21<(-u_xlat21));
#else
        u_xlatb21 = u_xlat21<(-u_xlat21);
#endif
        u_xlat21 = (u_xlatb21) ? (-u_xlat22) : u_xlat22;
        u_xlat21 = u_xlat15.x * u_xlat21 + -1.0;
        u_xlat2.xy = u_xlat7.xy * vec2(u_xlat21) + u_xlat1.xy;
    }
    u_xlat2.z = float(u_xlatu0);
    u_xlat10_0.xyz = texture(_SourceTex, u_xlat2.xyz).xyz;
#ifdef UNITY_ADRENO_ES3
    u_xlatb21 = !!(0.0<_Vignette_Params2.z);
#else
    u_xlatb21 = 0.0<_Vignette_Params2.z;
#endif
    if(u_xlatb21){
        u_xlat1.xy = u_xlat2.xy + (-_Vignette_Params2.xy);
        u_xlat1.yz = abs(u_xlat1.xy) * _Vignette_Params2.zz;
        u_xlat1.x = u_xlat1.y * _Vignette_Params1.w;
        u_xlat21 = dot(u_xlat1.xz, u_xlat1.xz);
        u_xlat21 = (-u_xlat21) + 1.0;
        u_xlat21 = max(u_xlat21, 0.0);
        u_xlat21 = log2(u_xlat21);
        u_xlat21 = u_xlat21 * _Vignette_Params2.w;
        u_xlat21 = exp2(u_xlat21);
        u_xlat1.xyz = (-_Vignette_Params1.xyz) + vec3(1.0, 1.0, 1.0);
        u_xlat1.xyz = vec3(u_xlat21) * u_xlat1.xyz + _Vignette_Params1.xyz;
        u_xlat1.xyz = u_xlat10_0.xyz * u_xlat1.xyz;
        u_xlat16_1.xyz = u_xlat1.xyz;
    } else {
        u_xlat16_1.xyz = u_xlat10_0.xyz;
    }
    u_xlat0.xyz = u_xlat16_1.xyz * _Lut_Params.www;
    u_xlat16_11.x = dot(vec3(0.439700991, 0.382977992, 0.177334994), u_xlat0.xyz);
    u_xlat16_11.y = dot(vec3(0.0897922963, 0.813422978, 0.0967615992), u_xlat0.xyz);
    u_xlat16_11.z = dot(vec3(0.0175439995, 0.111543998, 0.870703995), u_xlat0.xyz);
    u_xlat16_4.x = min(u_xlat16_11.y, u_xlat16_11.x);
    u_xlat16_4.x = min(u_xlat16_11.z, u_xlat16_4.x);
    u_xlat16_5.x = max(u_xlat16_11.y, u_xlat16_11.x);
    u_xlat16_5.x = max(u_xlat16_11.z, u_xlat16_5.x);
    u_xlat16_5.xy = max(u_xlat16_5.xx, vec2(9.99999975e-05, 0.00999999978));
    u_xlat16_4.x = max(u_xlat16_4.x, 9.99999975e-05);
    u_xlat16_4.x = (-u_xlat16_4.x) + u_xlat16_5.x;
    u_xlat16_4.x = u_xlat16_4.x / u_xlat16_5.y;
    u_xlat16_5.xyz = (-u_xlat16_11.yxz) + u_xlat16_11.zyx;
    u_xlat16_5.xy = u_xlat16_11.zy * u_xlat16_5.xy;
    u_xlat16_5.x = u_xlat16_5.y + u_xlat16_5.x;
    u_xlat16_5.x = u_xlat16_11.x * u_xlat16_5.z + u_xlat16_5.x;
    u_xlat0.x = max(u_xlat16_5.x, 0.0);
    u_xlat16_5.x = sqrt(u_xlat0.x);
    u_xlat16_12.x = u_xlat16_11.y + u_xlat16_11.z;
    u_xlat16_12.x = u_xlat16_11.x + u_xlat16_12.x;
    u_xlat16_5.x = u_xlat16_5.x * 1.75 + u_xlat16_12.x;
    u_xlat16_12.x = u_xlat16_5.x * 0.333333343;
    u_xlat0.x = u_xlat16_4.x + -0.400000006;
    u_xlat16_19 = u_xlat0.x * 2.5;
    u_xlat16_19 = -abs(u_xlat16_19) + 1.0;
    u_xlat16_19 = max(u_xlat16_19, 0.0);
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(u_xlat0.x>=0.0);
#else
    u_xlatb0.x = u_xlat0.x>=0.0;
#endif
    u_xlat0.x = (u_xlatb0.x) ? 1.0 : -1.0;
    u_xlat16_19 = (-u_xlat16_19) * u_xlat16_19 + 1.0;
    u_xlat0.x = u_xlat0.x * u_xlat16_19 + 1.0;
    u_xlat0.x = u_xlat0.x * 0.0250000004;
#ifdef UNITY_ADRENO_ES3
    u_xlatb7 = !!(0.159999996>=u_xlat16_5.x);
#else
    u_xlatb7 = 0.159999996>=u_xlat16_5.x;
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb14 = !!(u_xlat16_5.x>=0.479999989);
#else
    u_xlatb14 = u_xlat16_5.x>=0.479999989;
#endif
    u_xlat16_5.x = 0.0799999982 / u_xlat16_12.x;
    u_xlat16_5.x = u_xlat16_5.x + -0.5;
    u_xlat16_5.x = u_xlat0.x * u_xlat16_5.x;
    u_xlat16_5.x = (u_xlatb14) ? 0.0 : u_xlat16_5.x;
    u_xlat16_5.x = (u_xlatb7) ? u_xlat0.x : u_xlat16_5.x;
    u_xlat16_5.x = u_xlat16_5.x + 1.0;
    u_xlat0.yzw = u_xlat16_11.xyz * u_xlat16_5.xxx;
    u_xlatb2.xy = equal(u_xlat0.zwzz, u_xlat0.yzyy).xy;
    u_xlatb2.x = u_xlatb2.y && u_xlatb2.x;
    u_xlat16_18 = u_xlat16_11.y * u_xlat16_5.x + (-u_xlat0.w);
    u_xlat16_18 = u_xlat16_18 * 1.73205078;
    u_xlat16_12.x = u_xlat0.y * 2.0 + (-u_xlat0.z);
    u_xlat16_25 = (-u_xlat16_11.z) * u_xlat16_5.x + u_xlat16_12.x;
    u_xlat16_12.x = min(abs(u_xlat16_25), abs(u_xlat16_18));
    u_xlat16_19 = max(abs(u_xlat16_25), abs(u_xlat16_18));
    u_xlat16_19 = float(1.0) / u_xlat16_19;
    u_xlat16_12.x = u_xlat16_19 * u_xlat16_12.x;
    u_xlat16_19 = u_xlat16_12.x * u_xlat16_12.x;
    u_xlat9 = u_xlat16_19 * 0.0208350997 + -0.0851330012;
    u_xlat9 = u_xlat16_19 * u_xlat9 + 0.180141002;
    u_xlat9 = u_xlat16_19 * u_xlat9 + -0.330299497;
    u_xlat9 = u_xlat16_19 * u_xlat9 + 0.999866009;
    u_xlat16 = u_xlat9 * u_xlat16_12.x;
#ifdef UNITY_ADRENO_ES3
    u_xlatb23 = !!(abs(u_xlat16_25)<abs(u_xlat16_18));
#else
    u_xlatb23 = abs(u_xlat16_25)<abs(u_xlat16_18);
#endif
    u_xlat16 = u_xlat16 * -2.0 + 1.57079637;
    u_xlat16 = u_xlatb23 ? u_xlat16 : float(0.0);
    u_xlat9 = u_xlat16_12.x * u_xlat9 + u_xlat16;
#ifdef UNITY_ADRENO_ES3
    u_xlatb16 = !!(u_xlat16_25<(-u_xlat16_25));
#else
    u_xlatb16 = u_xlat16_25<(-u_xlat16_25);
#endif
    u_xlat16 = u_xlatb16 ? -3.14159274 : float(0.0);
    u_xlat9 = u_xlat16 + u_xlat9;
    u_xlat16_12.x = min(u_xlat16_25, u_xlat16_18);
    u_xlat16_18 = max(u_xlat16_25, u_xlat16_18);
#ifdef UNITY_ADRENO_ES3
    u_xlatb16 = !!(u_xlat16_12.x<(-u_xlat16_12.x));
#else
    u_xlatb16 = u_xlat16_12.x<(-u_xlat16_12.x);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb23 = !!(u_xlat16_18>=(-u_xlat16_18));
#else
    u_xlatb23 = u_xlat16_18>=(-u_xlat16_18);
#endif
    u_xlatb16 = u_xlatb23 && u_xlatb16;
    u_xlat9 = (u_xlatb16) ? (-u_xlat9) : u_xlat9;
    u_xlat16_18 = u_xlat9 * 57.2957802;
    u_xlat16_18 = (u_xlatb2.x) ? 0.0 : u_xlat16_18;
#ifdef UNITY_ADRENO_ES3
    u_xlatb2.x = !!(u_xlat16_18<0.0);
#else
    u_xlatb2.x = u_xlat16_18<0.0;
#endif
    u_xlat16_25 = u_xlat16_18 + 360.0;
    u_xlat16_18 = (u_xlatb2.x) ? u_xlat16_25 : u_xlat16_18;
#ifdef UNITY_ADRENO_ES3
    u_xlatb2.x = !!(u_xlat16_18<-180.0);
#else
    u_xlatb2.x = u_xlat16_18<-180.0;
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb9 = !!(180.0<u_xlat16_18);
#else
    u_xlatb9 = 180.0<u_xlat16_18;
#endif
    u_xlat16_12.xy = vec2(u_xlat16_18) + vec2(360.0, -360.0);
    u_xlat16_18 = (u_xlatb9) ? u_xlat16_12.y : u_xlat16_18;
    u_xlat16_18 = (u_xlatb2.x) ? u_xlat16_12.x : u_xlat16_18;
    u_xlat2.x = u_xlat16_18 * 0.0148148146;
    u_xlat2.x = -abs(u_xlat2.x) + 1.0;
    u_xlat2.x = max(u_xlat2.x, 0.0);
    u_xlat9 = u_xlat2.x * -2.0 + 3.0;
    u_xlat2.x = u_xlat2.x * u_xlat2.x;
    u_xlat2.x = u_xlat2.x * u_xlat9;
    u_xlat2.x = u_xlat2.x * u_xlat2.x;
    u_xlat2.x = u_xlat16_4.x * u_xlat2.x;
    u_xlat9 = (-u_xlat16_11.x) * u_xlat16_5.x + 0.0299999993;
    u_xlat2.x = u_xlat9 * u_xlat2.x;
    u_xlat0.x = u_xlat2.x * 0.180000007 + u_xlat0.y;
    u_xlat2.x = dot(vec3(1.45143926, -0.236510754, -0.214928567), u_xlat0.xzw);
    u_xlat2.y = dot(vec3(-0.0765537769, 1.17622972, -0.0996759236), u_xlat0.xzw);
    u_xlat2.z = dot(vec3(0.00831614807, -0.00603244966, 0.997716308), u_xlat0.xzw);
    u_xlat0.xyz = max(u_xlat2.xyz, vec3(0.0, 0.0, 0.0));
    u_xlat21 = dot(u_xlat0.xyz, vec3(0.272228986, 0.674081981, 0.0536894985));
    u_xlat0.xyz = (-vec3(u_xlat21)) + u_xlat0.xyz;
    u_xlat0.xyz = u_xlat0.xyz * vec3(0.959999979, 0.959999979, 0.959999979) + vec3(u_xlat21);
    u_xlat2.xyz = u_xlat0.xyz * vec3(2.78508496, 2.78508496, 2.78508496) + vec3(0.107772, 0.107772, 0.107772);
    u_xlat2.xyz = u_xlat0.xyz * u_xlat2.xyz;
    u_xlat3.xyz = u_xlat0.xyz * vec3(2.93604493, 2.93604493, 2.93604493) + vec3(0.887121975, 0.887121975, 0.887121975);
    u_xlat0.xyz = u_xlat0.xyz * u_xlat3.xyz + vec3(0.806888998, 0.806888998, 0.806888998);
    u_xlat0.xyz = u_xlat2.xyz / u_xlat0.xyz;
    u_xlat2.x = dot(vec3(0.662454188, 0.134004205, 0.156187683), u_xlat0.xyz);
    u_xlat2.y = dot(vec3(0.272228718, 0.674081743, 0.0536895171), u_xlat0.xyz);
    u_xlat2.z = dot(vec3(-0.00557464967, 0.0040607336, 1.01033914), u_xlat0.xyz);
    u_xlat16_4.x = dot(u_xlat2.xyz, vec3(1.0, 1.0, 1.0));
    u_xlat16_4.x = max(u_xlat16_4.x, 9.99999975e-05);
    u_xlat16_4.xy = u_xlat2.xy / u_xlat16_4.xx;
    u_xlat16_25 = max(u_xlat2.y, 0.0);
    u_xlat16_25 = min(u_xlat16_25, 65504.0);
    u_xlat16_25 = log2(u_xlat16_25);
    u_xlat16_25 = u_xlat16_25 * 0.981100023;
    u_xlat16_5.y = exp2(u_xlat16_25);
    u_xlat16_25 = max(u_xlat16_4.y, 9.99999975e-05);
    u_xlat16_25 = u_xlat16_5.y / u_xlat16_25;
    u_xlat16_26 = (-u_xlat16_4.x) + 1.0;
    u_xlat16_4.z = (-u_xlat16_4.y) + u_xlat16_26;
    u_xlat16_5.xz = vec2(u_xlat16_25) * u_xlat16_4.xz;
    u_xlat16_4.x = dot(vec3(1.6410234, -0.324803293, -0.236424699), u_xlat16_5.xyz);
    u_xlat16_4.y = dot(vec3(-0.663662851, 1.61533165, 0.0167563483), u_xlat16_5.xyz);
    u_xlat16_4.z = dot(vec3(0.0117218941, -0.00828444213, 0.988394856), u_xlat16_5.xyz);
    u_xlat0.x = dot(u_xlat16_4.xyz, vec3(0.272228986, 0.674081981, 0.0536894985));
    u_xlat7.xyz = (-u_xlat0.xxx) + u_xlat16_4.xyz;
    u_xlat0.xyz = u_xlat7.xyz * vec3(0.930000007, 0.930000007, 0.930000007) + u_xlat0.xxx;
    u_xlat2.x = dot(vec3(0.662454188, 0.134004205, 0.156187683), u_xlat0.xyz);
    u_xlat2.y = dot(vec3(0.272228718, 0.674081743, 0.0536895171), u_xlat0.xyz);
    u_xlat2.z = dot(vec3(-0.00557464967, 0.0040607336, 1.01033914), u_xlat0.xyz);
    u_xlat0.x = dot(vec3(0.987223983, -0.00611326983, 0.0159533005), u_xlat2.xyz);
    u_xlat0.y = dot(vec3(-0.00759836007, 1.00186002, 0.00533019984), u_xlat2.xyz);
    u_xlat0.z = dot(vec3(0.00307257008, -0.00509594986, 1.08168006), u_xlat2.xyz);
    u_xlat16_4.x = dot(vec3(3.2409699, -1.5373832, -0.498610765), u_xlat0.xyz);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_4.x = min(max(u_xlat16_4.x, 0.0), 1.0);
#else
    u_xlat16_4.x = clamp(u_xlat16_4.x, 0.0, 1.0);
#endif
    u_xlat16_4.y = dot(vec3(-0.969243646, 1.8759675, 0.0415550582), u_xlat0.xyz);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_4.y = min(max(u_xlat16_4.y, 0.0), 1.0);
#else
    u_xlat16_4.y = clamp(u_xlat16_4.y, 0.0, 1.0);
#endif
    u_xlat16_4.z = dot(vec3(0.0556300804, -0.203976959, 1.05697155), u_xlat0.xyz);
#ifdef UNITY_ADRENO_ES3
    u_xlat16_4.z = min(max(u_xlat16_4.z, 0.0), 1.0);
#else
    u_xlat16_4.z = clamp(u_xlat16_4.z, 0.0, 1.0);
#endif
#ifdef UNITY_ADRENO_ES3
    u_xlatb0.x = !!(0.0<_UserLut_Params.w);
#else
    u_xlatb0.x = 0.0<_UserLut_Params.w;
#endif
    if(u_xlatb0.x){
        u_xlat16_5.xyz = u_xlat16_4.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
        u_xlat16_6.xyz = log2(u_xlat16_4.xyz);
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
        u_xlat16_6.xyz = exp2(u_xlat16_6.xyz);
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat16_4.xyzx).xyz;
        {
            vec3 hlslcc_movcTemp = u_xlat16_5;
            hlslcc_movcTemp.x = (u_xlatb0.x) ? u_xlat16_5.x : u_xlat16_6.x;
            hlslcc_movcTemp.y = (u_xlatb0.y) ? u_xlat16_5.y : u_xlat16_6.y;
            hlslcc_movcTemp.z = (u_xlatb0.z) ? u_xlat16_5.z : u_xlat16_6.z;
            u_xlat16_5 = hlslcc_movcTemp;
        }
        u_xlat0.xyz = u_xlat16_5.zxy * _UserLut_Params.zzz;
        u_xlat0.x = floor(u_xlat0.x);
        u_xlat2.xy = _UserLut_Params.xy * vec2(0.5, 0.5);
        u_xlat2.yz = u_xlat0.yz * _UserLut_Params.xy + u_xlat2.xy;
        u_xlat2.x = u_xlat0.x * _UserLut_Params.y + u_xlat2.y;
        u_xlat10_7.xyz = textureLod(_UserLut, u_xlat2.xz, 0.0).xyz;
        u_xlat3.x = _UserLut_Params.y;
        u_xlat3.y = 0.0;
        u_xlat2.xy = u_xlat2.xz + u_xlat3.xy;
        u_xlat10_2.xyz = textureLod(_UserLut, u_xlat2.xy, 0.0).xyz;
        u_xlat0.x = u_xlat16_5.z * _UserLut_Params.z + (-u_xlat0.x);
        u_xlat2.xyz = (-u_xlat10_7.xyz) + u_xlat10_2.xyz;
        u_xlat0.xyz = u_xlat0.xxx * u_xlat2.xyz + u_xlat10_7.xyz;
        u_xlat0.xyz = (-u_xlat16_5.xyz) + u_xlat0.xyz;
        u_xlat0.xyz = _UserLut_Params.www * u_xlat0.xyz + u_xlat16_5.xyz;
        u_xlat16_5.xyz = u_xlat0.xyz * vec3(0.0773993805, 0.0773993805, 0.0773993805);
        u_xlat16_6.xyz = u_xlat0.xyz + vec3(0.0549999997, 0.0549999997, 0.0549999997);
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(0.947867334, 0.947867334, 0.947867334);
        u_xlat16_6.xyz = log2(abs(u_xlat16_6.xyz));
        u_xlat16_6.xyz = u_xlat16_6.xyz * vec3(2.4000001, 2.4000001, 2.4000001);
        u_xlat16_6.xyz = exp2(u_xlat16_6.xyz);
        u_xlatb0.xyz = greaterThanEqual(vec4(0.0404499993, 0.0404499993, 0.0404499993, 0.0), u_xlat0.xyzx).xyz;
        u_xlat16_4.x = (u_xlatb0.x) ? u_xlat16_5.x : u_xlat16_6.x;
        u_xlat16_4.y = (u_xlatb0.y) ? u_xlat16_5.y : u_xlat16_6.y;
        u_xlat16_4.z = (u_xlatb0.z) ? u_xlat16_5.z : u_xlat16_6.z;
    }
    u_xlat0.xyz = u_xlat16_4.zxy * _Lut_Params.zzz;
    u_xlat0.x = floor(u_xlat0.x);
    u_xlat2.xy = _Lut_Params.xy * vec2(0.5, 0.5);
    u_xlat2.yz = u_xlat0.yz * _Lut_Params.xy + u_xlat2.xy;
    u_xlat2.x = u_xlat0.x * _Lut_Params.y + u_xlat2.y;
    u_xlat10_7.xyz = textureLod(_InternalLut, u_xlat2.xz, 0.0).xyz;
    u_xlat3.x = _Lut_Params.y;
    u_xlat3.y = 0.0;
    u_xlat2.xy = u_xlat2.xz + u_xlat3.xy;
    u_xlat10_2.xyz = textureLod(_InternalLut, u_xlat2.xy, 0.0).xyz;
    u_xlat0.x = u_xlat16_4.z * _Lut_Params.z + (-u_xlat0.x);
    u_xlat2.xyz = (-u_xlat10_7.xyz) + u_xlat10_2.xyz;
    u_xlat0.xyz = u_xlat0.xxx * u_xlat2.xyz + u_xlat10_7.xyz;
    u_xlat16_4.xyz = u_xlat0.xyz * vec3(12.9200001, 12.9200001, 12.9200001);
    u_xlat16_5.xyz = log2(abs(u_xlat0.xyz));
    u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(0.416666657, 0.416666657, 0.416666657);
    u_xlat16_5.xyz = exp2(u_xlat16_5.xyz);
    u_xlat16_5.xyz = u_xlat16_5.xyz * vec3(1.05499995, 1.05499995, 1.05499995) + vec3(-0.0549999997, -0.0549999997, -0.0549999997);
    u_xlatb0.xyz = greaterThanEqual(vec4(0.00313080009, 0.00313080009, 0.00313080009, 0.0), u_xlat0.xyzx).xyz;
    SV_Target0.x = (u_xlatb0.x) ? u_xlat16_4.x : u_xlat16_5.x;
    SV_Target0.y = (u_xlatb0.y) ? u_xlat16_4.y : u_xlat16_5.y;
    SV_Target0.z = (u_xlatb0.z) ? u_xlat16_4.z : u_xlat16_5.z;
    SV_Target0.w = 1.0;
    return;
}

#endif
                              $Globals`         _Lut_Params                          _UserLut_Params                         _Distortion_Params1                          _Distortion_Params2                   0      _Vignette_Params1                     @      _Vignette_Params2                     P          $Globals      
   _ScaleBias                               UnityStereoEyeIndices             unity_StereoEyeIndices                              
   _SourceTex              
      _InternalLut                _UserLut                UnityStereoEyeIndices                  